from dotenv import load_dotenv
import os
import pandas as pd
from llama_index.experimental.query_engine import PandasQueryEngine
from llama_index.llms.openai import OpenAI
from pydantic import BaseModel
from typing import List

load_dotenv()

# Configure pandas to avoid matplotlib plotting issues
pd.options.plotting.backend = "matplotlib"
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend

# ----------------------------
# Patch safe_eval to fix deprecated .dt.week usage in llama_index
# ----------------------------
from llama_index.experimental import exec_utils

_original_safe_eval = exec_utils.safe_eval

def patched_safe_eval(__source, __globals=None, __locals=None):
    fixed_source = __source.replace(".dt.week", ".dt.isocalendar().week")
    return _original_safe_eval(fixed_source, __globals, __locals)

exec_utils.safe_eval = patched_safe_eval

# ----------------------------
# Global Configuration
# ----------------------------
def get_global_chart_colors():
    """
    Get global chart colors from smart dashboard configuration
    Returns the same color palette used in frontend for consistency
    """
    return [
        '#ffb366', '#ffc999', '#ffab66', '#ffd6b3', '#ff9d4d',
        '#ffe0cc', '#ffb84d', '#fff2e6', '#ffa64d', '#fff5f0',
        '#4e79a7', '#f28e2c', '#e15759', '#76b7b2', '#59a14f'
    ]

def get_semantic_colors():
    """
    Get semantic color mappings for different chart types and data meanings
    Uses global colors but assigns them semantic meaning
    """
    global_colors = get_global_chart_colors()
    return {
        # Movement types
        'inward': global_colors[4],      # '#ff9d4d' - Orange for positive/inward
        'outward': global_colors[12],    # '#e15759' - Red for negative/outward
        'neutral': global_colors[10],    # '#4e79a7' - Blue for neutral
        'positive': global_colors[14],   # '#59a14f' - Green for positive metrics
        'negative': global_colors[12],   # '#e15759' - Red for negative metrics
        'warning': global_colors[11],    # '#f28e2c' - Orange for warnings

        # Chart type defaults
        'primary': global_colors[0],     # '#ffb366' - Primary brand color
        'secondary': global_colors[1],   # '#ffc999' - Secondary brand color
        'accent': global_colors[2],      # '#ffab66' - Accent color

        # Specific use cases
        'purchase': global_colors[0],    # '#ffb366' - Purchase related
        'inventory': global_colors[13],  # '#76b7b2' - Inventory related
        'sales': global_colors[14],      # '#59a14f' - Sales related
        'spoilage': global_colors[12],   # '#e15759' - Loss/spoilage
        'transfer': global_colors[10],   # '#4e79a7' - Transfers

        # UI colors
        'border_light': '#ffffff',       # White borders
        'border_dark': '#333333',        # Dark borders
        'grid': '#e9ecef',              # Grid lines
    }

# ----------------------------
# Chart.js Data Models
# ----------------------------
class ChartDataset(BaseModel):
    label: str
    data: List[float]
    backgroundColor: List[str]
    borderColor: List[str]

class ChartData(BaseModel):
    labels: List[str]
    datasets: List[ChartDataset]

class Chart(BaseModel):
    id: str
    title: str
    type: str
    data: ChartData

class SummaryItem(BaseModel):
    icon: str
    value: str
    label: str
    data_type: str

class DashboardResponse(BaseModel):
    charts: List[Chart]
    summary_items: List[SummaryItem]

# ----------------------------
# Main Dashboard Function
# ----------------------------
def generate_purchase_dashboard(df: pd.DataFrame) -> dict:
    """
    Generate Purchase Department dashboard with fixed charts and summary cards
    without using LLM - direct DataFrame analysis
    """
    try:
        # Get global chart colors for consistency
        colors = get_global_chart_colors()
        semantic_colors = get_semantic_colors()

        charts = []
        summary_items = []

        if df.empty:
            return {
                "success": True,
                "charts": [],
                "summary_items": [
                    {
                        "icon": "warning",
                        "value": "No Data",
                        "label": "Purchase Records",
                        "data_type": "text"
                    }
                ]
            }

        # 1. Total Purchase Cost (Summary Item)
        if 'Total(incl.tax,etc)' in df.columns:
            total_cost = df['Total(incl.tax,etc)'].sum()
            summary_items.append({
                "icon": "currency_rupee",
                "value": f"₹{total_cost:,.2f}",
                "label": "Total Purchase Cost",
                "data_type": "currency"
            })

        # 2. Total Items Purchased (Summary Item)
        if 'Received Qty' in df.columns:
            total_items = df['Received Qty'].sum()
            summary_items.append({
                "icon": "inventory",
                "value": f"{total_items:,.0f}",
                "label": "Total Items Received",
                "data_type": "number"
            })

        # 3. Average Cost per Item (Summary Item)
        if 'Total(incl.tax,etc)' in df.columns and 'Received Qty' in df.columns:
            avg_cost = total_cost / total_items if total_items > 0 else 0
            summary_items.append({
                "icon": "calculate",
                "value": f"₹{avg_cost:.2f}",
                "label": "Average Cost per Item",
                "data_type": "currency"
            })

        # 4. Purchase Order Accuracy (Summary Item)
        if 'Order Qty' in df.columns and 'Received Qty' in df.columns:
            total_ordered = df['Order Qty'].sum()
            total_received = df['Received Qty'].sum()
            accuracy = (total_received / total_ordered * 100) if total_ordered > 0 else 0
            summary_items.append({
                "icon": "check_circle",
                "value": f"{accuracy:.1f}%",
                "label": "Purchase Order Accuracy",
                "data_type": "percentage"
            })

        # 5. Purchase Cost by Location (Bar Chart)
        if 'Location' in df.columns and 'Total(incl.tax,etc)' in df.columns:
            location_costs = df.groupby('Location')['Total(incl.tax,etc)'].sum().sort_values(ascending=False)
            if not location_costs.empty:
                charts.append({
                    "id": "purchase_cost_by_location",
                    "title": "Purchase Cost by Location",
                    "type": "bar",
                    "data": {
                        "labels": location_costs.index.tolist(),
                        "datasets": [{
                            "label": "Purchase Cost (₹)",
                            "data": location_costs.values.tolist(),
                            "backgroundColor": colors[:len(location_costs)],
                            "borderColor": colors[:len(location_costs)]
                        }]
                    }
                })

        # 6. Purchase Cost by Vendor (Bar Chart)
        if 'Vendor Name' in df.columns and 'Total(incl.tax,etc)' in df.columns:
            vendor_costs = df.groupby('Vendor Name')['Total(incl.tax,etc)'].sum().sort_values(ascending=False).head(10)
            if not vendor_costs.empty:
                charts.append({
                    "id": "purchase_cost_by_vendor",
                    "title": "Top 10 Vendors by Purchase Cost",
                    "type": "bar",
                    "data": {
                        "labels": vendor_costs.index.tolist(),
                        "datasets": [{
                            "label": "Purchase Cost (₹)",
                            "data": vendor_costs.values.tolist(),
                            "backgroundColor": colors[:len(vendor_costs)],
                            "borderColor": colors[:len(vendor_costs)]
                        }]
                    }
                })

        # 7. Purchase by Category (Doughnut Chart)
        if 'Category' in df.columns and 'Total(incl.tax,etc)' in df.columns:
            category_costs = df.groupby('Category')['Total(incl.tax,etc)'].sum().sort_values(ascending=False)
            if not category_costs.empty:
                charts.append({
                    "id": "purchase_by_category",
                    "title": "Purchase Distribution by Category",
                    "type": "doughnut",
                    "data": {
                        "labels": category_costs.index.tolist(),
                        "datasets": [{
                            "label": "Purchase Cost (₹)",
                            "data": category_costs.values.tolist(),
                            "backgroundColor": colors[:len(category_costs)],
                            "borderColor": colors[:len(category_costs)]
                        }]
                    }
                })

        # 8. Cost Trends Over Time (Line Chart)
        # Look for date columns - GRN has specific date column names
        date_columns = []
        for col in df.columns:
            if any(date_word in col.lower() for date_word in ['date', 'entry']):
                date_columns.append(col)

        if date_columns and 'Total(incl.tax,etc)' in df.columns:
            date_col = date_columns[0]  # Use first date column found
            try:
                df_copy = df.copy()
                # Handle different date formats
                if df_copy[date_col].dtype == 'object':
                    df_copy[date_col] = pd.to_datetime(df_copy[date_col], errors='coerce')
                daily_costs = df_copy.groupby(df_copy[date_col].dt.date)['Total(incl.tax,etc)'].sum().sort_index()
                if len(daily_costs) > 1:
                    charts.append({
                        "id": "cost_trends",
                        "title": "Purchase Cost Trends Over Time",
                        "type": "line",
                        "data": {
                            "labels": [str(date) for date in daily_costs.index],
                            "datasets": [{
                                "label": "Daily Purchase Cost (₹)",
                                "data": daily_costs.values.tolist(),
                                "backgroundColor": [semantic_colors['purchase']],
                                "borderColor": [semantic_colors['purchase']]
                            }]
                        }
                    })
            except Exception as e:
                print(f"Error creating cost trends chart: {e}")
                pass  # Skip if date parsing fails

        # 9. Top 15 Purchase Items (Bar Chart)
        if 'Item Name' in df.columns and 'Total(incl.tax,etc)' in df.columns:
            item_costs = df.groupby('Item Name')['Total(incl.tax,etc)'].sum().sort_values(ascending=False).head(15)
            if not item_costs.empty:
                charts.append({
                    "id": "top_purchase_items",
                    "title": "Top 15 Purchase Items by Cost",
                    "type": "bar",
                    "data": {
                        "labels": item_costs.index.tolist(),
                        "datasets": [{
                            "label": "Purchase Cost (₹)",
                            "data": item_costs.values.tolist(),
                            "backgroundColor": colors[:len(item_costs)],
                            "borderColor": colors[:len(item_costs)]
                        }]
                    }
                })

        # 10. Cost per Item Analysis (Bar Chart)
        if 'Item Name' in df.columns and 'Total(incl.tax,etc)' in df.columns and 'Received Qty' in df.columns:
            df_filtered = df[df['Received Qty'] > 0].copy()
            df_filtered['Cost_per_Item'] = df_filtered['Total(incl.tax,etc)'] / df_filtered['Received Qty']
            cost_per_item = df_filtered.groupby('Item Name')['Cost_per_Item'].mean().sort_values(ascending=False).head(10)
            if not cost_per_item.empty:
                charts.append({
                    "id": "cost_per_item",
                    "title": "Top 10 Items by Average Cost per Unit",
                    "type": "bar",
                    "data": {
                        "labels": cost_per_item.index.tolist(),
                        "datasets": [{
                            "label": "Cost per Unit (₹)",
                            "data": cost_per_item.values.tolist(),
                            "backgroundColor": colors[:len(cost_per_item)],
                            "borderColor": colors[:len(cost_per_item)]
                        }]
                    }
                })

        # 11. Total Vendors Count (Summary Item)
        if 'Vendor Name' in df.columns:
            unique_vendors = df['Vendor Name'].nunique()
            summary_items.append({
                "icon": "business",
                "value": f"{unique_vendors}",
                "label": "Active Vendors",
                "data_type": "number"
            })

        return {
            "success": True,
            "charts": charts,
            "summary_items": summary_items
        }

    except Exception as e:
        print(f"Error generating purchase dashboard: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "charts": [],
            "summary_items": []
        }


def generate_inventory_dashboard(df: pd.DataFrame) -> dict:
    """
    Generate comprehensive Inventory Management dashboard based on store_variance DataFrame
    Focused on essential business insights with non-redundant, high-value charts
    """
    try:
        # Dashboard generation with enhanced user experience

        charts = []
        summary_items = []

        if df.empty:
            return {
                "success": True,
                "charts": [],
                "summary_items": [
                    {
                        "icon": "warning",
                        "value": "No Data",
                        "label": "Inventory Records",
                        "data_type": "text"
                    }
                ]
            }

        # Calculate derived metrics for period analysis
        df_calc = df.copy()

        # Calculate Return To Store In Amount (items sent back TO store from work areas)
        df_calc['Return To Store In Amount'] = (
            df_calc.get('Return To Store In Qty', 0) * df_calc.get('WAC(incl.tax,etc)', 0)
        )

        # Calculate period movements with correct business logic
        df_calc['Period_Net_Inward'] = (
            df_calc.get('Purchase Amount', 0) +
            df_calc.get('Ibt In Amount', 0) +
            df_calc.get('Return To Store In Amount', 0)  # Items returned TO store (inward)
        )

        df_calc['Period_Net_Outward'] = (
            df_calc.get('Return-Qty Amount', 0) +  # Items returned to vendor (outward)
            df_calc.get('Indent Amount', 0) +
            df_calc.get('Ibt Out Amount', 0) +
            df_calc.get('Spoilage Amount', 0)
        )

        df_calc['Period_Net_Movement'] = df_calc['Period_Net_Inward'] - df_calc['Period_Net_Outward']

        # Turnover calculation (more robust)
        df_calc['Avg_Inventory'] = (df_calc.get('Opening Amount', 0) + df_calc.get('Closing Amount', 0)) / 2
        df_calc['Period_Turnover'] = df_calc.apply(
            lambda row: row['Period_Net_Outward'] / row['Avg_Inventory'] if row['Avg_Inventory'] > 0 else 0, axis=1
        )

        # Growth rate calculation
        df_calc['Period_Growth_Rate'] = df_calc.apply(
            lambda row: ((row.get('Closing Amount', 0) - row.get('Opening Amount', 0)) /
                        row.get('Opening Amount', 1)) * 100 if row.get('Opening Amount', 0) > 0 else 0, axis=1
        )

        # ABC Classification based on closing value
        df_calc = df_calc.sort_values('Closing Amount', ascending=False)
        total_value = df_calc['Closing Amount'].sum()
        df_calc['Cumulative_Percentage'] = (df_calc['Closing Amount'].cumsum() / total_value) * 100
        df_calc['ABC_Class'] = df_calc['Cumulative_Percentage'].apply(
            lambda x: 'A' if x <= 70 else ('B' if x <= 90 else 'C')
        )

        # === COMPREHENSIVE INVENTORY MOVEMENT KPIs (15 Cards) ===

        # Calculate all movement types - Convert to native Python types
        total_opening = float(df_calc.get('Opening Amount', pd.Series([0])).sum())
        total_closing = float(df_calc.get('Closing Amount', pd.Series([0])).sum())
        net_growth = float(((total_closing - total_opening) / total_opening) * 100) if total_opening > 0 else 0.0

        total_purchases = float(df_calc.get('Purchase Amount', pd.Series([0])).sum())
        total_indents = float(df_calc.get('Indent Amount', pd.Series([0])).sum())
        total_ibt_in = float(df_calc.get('Ibt In Amount', pd.Series([0])).sum())
        total_ibt_out = float(df_calc.get('Ibt Out Amount', pd.Series([0])).sum())
        total_spoilage = float(df_calc.get('Spoilage Amount', pd.Series([0])).sum())
        total_returns_to_store = float(df_calc.get('Return To Store In Amount', pd.Series([0])).sum())
        total_returns_to_vendor = float(df_calc.get('Return-Qty Amount', pd.Series([0])).sum())

        total_inward = float(df_calc['Period_Net_Inward'].sum())
        total_outward = float(df_calc['Period_Net_Outward'].sum())
        avg_turnover = float(df_calc['Period_Turnover'].mean()) if not df_calc['Period_Turnover'].empty else 0.0

        # Row 1: Stock Position (Consolidated with Growth %)
        summary_items.extend([
            {
                "icon": "trending_up",
                "value": f"₹{total_opening:,.0f}",
                "label": "Opening Stock Value",
                "data_type": "currency"
            },
            {
                "icon": "account_balance_wallet" if net_growth >= 0 else "trending_down",
                "value": f"₹{total_closing:,.0f}",
                "label": f"Closing Stock Value ({net_growth:+.1f}%)",
                "data_type": "currency",
                "secondary_info": f"Growth: {net_growth:+.1f}% from opening",
                "trend": "positive" if net_growth >= 0 else "negative"
            }
        ])

        # Row 2: Inward Movements (Items Coming IN)
        summary_items.extend([
            {
                "icon": "shopping_cart",
                "value": f"₹{total_purchases:,.0f}",
                "label": "Purchases (from Vendor)",
                "data_type": "currency"
            },
            {
                "icon": "input",
                "value": f"₹{total_ibt_in:,.0f}",
                "label": "IBT In (from Locations)",
                "data_type": "currency"
            },
            {
                "icon": "keyboard_return",
                "value": f"₹{total_returns_to_store:,.0f}",
                "label": "Returns to Store (from Work Areas)",
                "data_type": "currency"
            }
        ])

        # Row 3: Outward Movements (Items Going OUT)
        summary_items.extend([
            {
                "icon": "construction",
                "value": f"₹{total_indents:,.0f}",
                "label": "Indents/Usage (Consumed)",
                "data_type": "currency"
            },
            {
                "icon": "output",
                "value": f"₹{total_ibt_out:,.0f}",
                "label": "IBT Out (to Locations)",
                "data_type": "currency"
            },
            {
                "icon": "undo",
                "value": f"₹{total_returns_to_vendor:,.0f}",
                "label": "Returns to Vendor",
                "data_type": "currency"
            }
        ])

        # Row 4: Loss & Efficiency Metrics (Consolidated Spoilage)
        active_skus = int(len(df_calc[df_calc['Period_Net_Outward'] > 0]))
        total_skus = int(len(df_calc))
        spoilage_rate = float((total_spoilage / total_outward * 100)) if total_outward > 0 else 0.0

        summary_items.extend([
            {
                "icon": "warning" if spoilage_rate > 5.0 else "info",
                "value": f"₹{total_spoilage:,.0f}",
                "label": f"Spoilage/Loss ({spoilage_rate:.1f}%)",
                "data_type": "currency",
                "secondary_info": f"Rate: {spoilage_rate:.1f}% of total outward movement",
                "trend": "negative" if spoilage_rate > 3.0 else "neutral"
            },
            {
                "icon": "inventory",
                "value": f"{active_skus}/{total_skus}",
                "label": "Active SKUs",
                "data_type": "text",
                "secondary_info": f"Utilization: {(active_skus/total_skus*100):.1f}% of inventory items active"
            }
        ])

        # Row 5: Summary Totals & Performance (Enhanced Turnover Context)
        # Determine turnover performance level and context
        if avg_turnover >= 2.0:
            turnover_status = "Excellent"
            turnover_icon = "trending_up"
            turnover_trend = "positive"
            turnover_context = "Outstanding cash flow efficiency"
        elif avg_turnover >= 1.5:
            turnover_status = "Good"
            turnover_icon = "thumb_up"
            turnover_trend = "positive"
            turnover_context = "Healthy inventory performance"
        elif avg_turnover >= 1.0:
            turnover_status = "Average"
            turnover_icon = "sync"
            turnover_trend = "neutral"
            turnover_context = "Room for improvement"
        else:
            turnover_status = "Poor"
            turnover_icon = "warning"
            turnover_trend = "negative"
            turnover_context = "Needs immediate attention"

        # Calculate net movement for enhanced insights
        net_movement = total_inward - total_outward
        net_movement_status = "positive" if net_movement > 0 else "negative" if net_movement < 0 else "neutral"

        summary_items.extend([
            {
                "icon": "sync_alt" if net_movement_status == "neutral" else "trending_up" if net_movement_status == "positive" else "trending_down",
                "value": f"₹{abs(net_movement):,.0f}",
                "label": f"Net Movement ({'Build-up' if net_movement > 0 else 'Consumption' if net_movement < 0 else 'Balanced'})",
                "data_type": "currency",
                "secondary_info": f"In: ₹{total_inward:,.0f} | Out: ₹{total_outward:,.0f} | {'Inventory increasing' if net_movement > 0 else 'Inventory decreasing' if net_movement < 0 else 'Perfectly balanced'}",
                "trend": net_movement_status
            },
            {
                "icon": turnover_icon,
                "value": f"{avg_turnover:.2f}x",
                "label": f"Turnover Efficiency ({turnover_status})",
                "data_type": "number",
                "secondary_info": f"{turnover_context} | Industry benchmark: 1.5-2.0x | {'🚀 Excellent cash flow!' if avg_turnover >= 2.0 else '👍 Good performance' if avg_turnover >= 1.5 else '⚠️ Needs improvement' if avg_turnover >= 1.0 else '🚨 Urgent attention needed'}",
                "trend": turnover_trend
            }
        ])

        # === INVENTORY DASHBOARD CHARTS - ORDERED BY BUSINESS IMPORTANCE ===
        # Priority Order: Critical Operations → Strategic Management → Performance Monitoring

        # 🚨 TIER 1: CRITICAL DAILY OPERATIONS (Must See First)

        # 1. CRITICAL: Physical vs System Stock Variance - IMMEDIATE ACTION REQUIRED
        if all(col in df.columns for col in ['Physical Closing Qty', 'Closing Qty', 'Variance Amount', 'Category']):
            # Calculate variance analysis by category
            variance_analysis = df_calc.groupby('Category').agg({
                'Variance Amount': 'sum',
                'Physical Closing Amount': 'sum',
                'Closing Amount': 'sum',
                'Item Code': 'count'
            }).round(2)

            # Calculate variance percentage
            variance_analysis['Variance_Percentage'] = (
                variance_analysis['Variance Amount'] / variance_analysis['Closing Amount'] * 100
            ).fillna(0)

            # Filter categories with significant variances
            significant_variances = variance_analysis[
                (abs(variance_analysis['Variance Amount']) > 100) |
                (abs(variance_analysis['Variance_Percentage']) > 1.0)
            ].nlargest(8, 'Variance Amount', keep='all')

            if not significant_variances.empty:
                categories = significant_variances.index.tolist()
                variance_amounts = [float(val) for val in significant_variances['Variance Amount'].tolist()]
                variance_percentages = [float(val) for val in significant_variances['Variance_Percentage'].tolist()]

                # Color code based on variance severity
                variance_colors = []
                for amount, percentage in zip(variance_amounts, variance_percentages):
                    if abs(amount) > 5000 or abs(percentage) > 10:
                        variance_colors.append('#E74C3C')  # Critical - Red
                    elif abs(amount) > 2000 or abs(percentage) > 5:
                        variance_colors.append('#F39C12')  # High - Orange
                    elif abs(amount) > 500 or abs(percentage) > 2:
                        variance_colors.append('#F1C40F')  # Medium - Yellow
                    else:
                        variance_colors.append('#27AE60')  # Low - Green

                # Create info text for variance analysis
                variance_info_text = """
                🚨 Physical vs System Stock Variance - IMMEDIATE ACTION REQUIRED

                📊 What This Shows:
                • Variance Amount: Difference between physical count and system records (₹)
                • Positive variance: Physical stock is higher than system records
                • Negative variance: Physical stock is lower than system records (potential shrinkage)

                ⚠️ Why This Is CRITICAL:
                • Large variances indicate inventory control issues
                • Consistent negative variances suggest theft, spoilage, or recording errors
                • Positive variances may indicate missed receipts or recording delays
                • Accurate inventory is crucial for cost control and ordering decisions

                🎯 URGENT Action Required:
                • 🔴 Critical (>₹5K or >10%): Immediate investigation needed
                • 🟡 High (>₹2K or >5%): Review processes and controls
                • 🟡 Medium (>₹500 or >2%): Monitor closely, improve procedures
                • 🟢 Low (<₹500 and <2%): Acceptable variance range
                """

                charts.append({
                    "id": "physical_vs_system_variance",
                    "title": "🚨 Physical vs System Stock Variance - IMMEDIATE ACTION",
                    "subtitle": "Critical: Identify discrepancies between physical counts and system records",
                    "info": variance_info_text,
                    "type": "bar",
                    "data": {
                        "labels": categories,
                        "datasets": [{
                            "label": "Variance Amount (₹)",
                            "data": variance_amounts,
                            "backgroundColor": variance_colors,
                            "borderColor": '#FFFFFF',
                            "borderWidth": 2,
                            "borderRadius": 6
                        }]
                    },
                    "options": {
                        "responsive": True,
                        "maintainAspectRatio": False,
                        "plugins": {
                            "legend": {"display": False},
                            "title": {
                                "display": True,
                                "text": "🔴 Critical | 🟠 High | 🟡 Medium | 🟢 Low Variance - Address Critical Issues First",
                                "font": {"size": 11, "style": "italic"},
                                "color": "#666"
                            },
                            "tooltip": {
                                "callbacks": {
                                    "title": "function(context) { return '⚠️ URGENT: ' + context[0].label; }",
                                    "label": "function(context) { var amount = context.parsed.y; var percentages = " + str(variance_percentages) + "; var percentage = percentages[context.dataIndex]; return 'Variance: ₹' + amount.toLocaleString() + ' (' + percentage.toFixed(2) + '%)'; }",
                                    "afterLabel": "function(context) { var amount = context.parsed.y; if(amount > 0) return '📈 Physical > System (Excess found)'; else if(amount < 0) return '📉 Physical < System (Shortage detected)'; else return '✅ Perfect match'; }",
                                    "footer": "function(context) { var amount = Math.abs(context.parsed.y); var percentage = Math.abs(" + str(variance_percentages) + "[context.dataIndex]); if(amount > 5000 || percentage > 10) return '🚨 CRITICAL: Immediate investigation required'; else if(amount > 2000 || percentage > 5) return '⚠️ HIGH: Review processes'; else if(amount > 500 || percentage > 2) return '📋 MEDIUM: Monitor closely'; else return '✅ LOW: Acceptable variance'; }"
                                }
                            }
                        },
                        "scales": {
                            "x": {
                                "grid": {"display": False},
                                "ticks": {"font": {"size": 10}}
                            },
                            "y": {
                                "grid": {"color": "#E5E5E5"},
                                "title": {
                                    "display": True,
                                    "text": "Variance Amount (₹) - Positive = Excess, Negative = Shortage",
                                    "font": {"size": 12}
                                },
                                "ticks": {
                                    "font": {"size": 10},
                                    "callback": "function(value) { return '₹' + (value/1000).toFixed(1) + 'K'; }"
                                }
                            }
                        }
                    }
                })

        # 2. CRITICAL: Smart Risk Intelligence - URGENT ATTENTION NEEDED
        if 'Spoilage Amount' in df.columns and 'Category' in df.columns:
            # Comprehensive risk analysis combining multiple factors
            risk_analysis = df_calc.groupby('Category').agg({
                'Spoilage Amount': 'sum',
                'Period_Turnover': 'mean',
                'Closing Amount': 'sum',
                'Opening Amount': 'sum',
                'Item Code': 'count'
            }).fillna(0)

            # Calculate comprehensive risk metrics
            risk_analysis['Spoilage_Rate'] = (risk_analysis['Spoilage Amount'] / risk_analysis['Closing Amount'] * 100).fillna(0)
            risk_analysis['Turnover_Risk'] = risk_analysis['Period_Turnover'].apply(lambda x: 10 if x < 0.5 else (5 if x < 1.0 else (2 if x < 2.0 else 0)))
            risk_analysis['Value_Risk'] = (risk_analysis['Closing Amount'] / risk_analysis['Closing Amount'].sum() * 100).apply(lambda x: 10 if x > 20 else (5 if x > 10 else 0))
            risk_analysis['Overall_Risk_Score'] = risk_analysis['Spoilage_Rate'] + risk_analysis['Turnover_Risk'] + risk_analysis['Value_Risk']

            # Get categories with significant risk (top 6)
            high_risk_categories = risk_analysis[risk_analysis['Overall_Risk_Score'] > 0].nlargest(6, 'Overall_Risk_Score')

            if not high_risk_categories.empty:
                categories = high_risk_categories.index.tolist()
                spoilage_rates = [float(val) for val in high_risk_categories['Spoilage_Rate'].round(2).tolist()]
                risk_scores = [float(val) for val in high_risk_categories['Overall_Risk_Score'].round(1).tolist()]

                # Color code based on comprehensive risk level
                risk_colors = []
                risk_labels = []
                for score in risk_scores:
                    if score > 20:
                        risk_colors.append('#C0392B')  # Critical - Dark Red
                        risk_labels.append('🚨 Critical')
                    elif score > 15:
                        risk_colors.append('#E74C3C')  # High - Red
                        risk_labels.append('🔴 High Risk')
                    elif score > 10:
                        risk_colors.append('#F39C12')  # Medium - Orange
                        risk_labels.append('⚠️ Medium Risk')
                    else:
                        risk_colors.append('#F1C40F')  # Low - Yellow
                        risk_labels.append('⚡ Watch')

                # Create comprehensive risk info
                risk_info_text = """
                🚨 Smart Risk Intelligence - URGENT ATTENTION NEEDED

                🎯 What This Analysis Does:
                • Combines 3 critical risk factors: Spoilage Rate + Turnover Speed + Value Impact
                • Identifies categories that could hurt your bottom line the most
                • Prioritizes where to focus your management attention first

                📊 Risk Score Components:
                • Spoilage Risk: High waste rates = direct profit loss
                • Turnover Risk: Slow-moving inventory = cash tied up
                • Value Risk: High-value categories = bigger financial impact

                💰 Why This Matters:
                • High-risk categories can drain profits quickly
                • Early identification prevents bigger losses
                • Focused action on top risks gives maximum ROI

                🎯 URGENT Action Guide by Risk Level:
                • 🚨 Critical (20+): URGENT - Daily monitoring, immediate process changes
                • 🔴 High (15+): Weekly reviews, implement controls, staff training
                • ⚠️ Medium (10+): Monthly checks, process improvements
                • ⚡ Watch (<10): Quarterly review, maintain current practices
                """

                charts.append({
                    "id": "smart_risk_intelligence",
                    "title": "🚨Smart Risk Intelligence - URGENT ATTENTION NEEDED",
                    "subtitle": "Critical: Multi-factor risk assessment to prioritize management attention",
                    "info": risk_info_text,
                    "type": "bar",
                    "data": {
                        "labels": categories,
                        "datasets": [
                            {
                                "label": "🔍 Spoilage Rate (%)",
                                "data": spoilage_rates,
                                "backgroundColor": risk_colors,
                                "borderColor": '#FFFFFF',
                                "borderWidth": 2,
                                "borderRadius": 6,
                                "yAxisID": 'y'
                            }
                        ]
                    },
                    "options": {
                        "responsive": True,
                        "maintainAspectRatio": False,
                        "plugins": {
                            "legend": {"display": False},
                            "title": {
                                "display": True,
                                "text": "🚨 Critical | 🔴 High Risk | ⚠️ Medium Risk | ⚡ Watch - Focus on highest risk first",
                                "font": {"size": 11, "style": "italic"},
                                "color": "#666"
                            },
                            "tooltip": {
                                "callbacks": {
                                    "title": "function(context) { return '⚠️ Risk Category: ' + context[0].label; }",
                                    "label": "function(context) { var spoilageRate = context.parsed.y; var riskLabels = " + str(risk_labels) + "; var riskScores = " + str(risk_scores) + "; return ['Spoilage Rate: ' + spoilageRate.toFixed(2) + '%', 'Overall Risk Score: ' + riskScores[context.dataIndex]]; }",
                                    "afterLabel": "function(context) { var riskLabels = " + str(risk_labels) + "; return 'Risk Level: ' + riskLabels[context.dataIndex]; }",
                                    "footer": "function(context) { var riskLabels = " + str(risk_labels) + "; var label = riskLabels[context.dataIndex]; if(label.includes('Critical')) return '🚨 URGENT: Daily monitoring, immediate action required'; else if(label.includes('High')) return '🔴 HIGH: Weekly reviews, implement controls'; else if(label.includes('Medium')) return '⚠️ MEDIUM: Monthly checks, process improvements'; else return '⚡ WATCH: Quarterly review, maintain practices'; }"
                                }
                            }
                        },
                        "scales": {
                            "x": {
                                "grid": {"display": False},
                                "ticks": {"font": {"size": 10}}
                            },
                            "y": {
                                "beginAtZero": True,
                                "grid": {"color": "#E5E5E5"},
                                "title": {
                                    "display": True,
                                    "text": "Spoilage Rate (%) - Visual indicator of risk level",
                                    "font": {"size": 12}
                                },
                                "ticks": {
                                    "font": {"size": 10},
                                    "callback": "function(value) { return value.toFixed(1) + '%'; }"
                                }
                            }
                        }
                    }
                })

        # 💰 TIER 2: STRATEGIC MANAGEMENT (High Value)

        # 3. STRATEGIC: Complete Inventory Flow Tracking - FINANCIAL OVERVIEW
        if all(col in df.columns for col in ['Opening Amount', 'Purchase Amount', 'Indent Amount', 'Spoilage Amount', 'Closing Amount']):
            # Calculate all movements with proper visibility for small amounts
            opening_val = float(df_calc.get('Opening Amount', pd.Series([0])).sum())
            purchases_val = float(df_calc.get('Purchase Amount', pd.Series([0])).sum())
            ibt_in_val = float(df_calc.get('Ibt In Amount', pd.Series([0])).sum())
            returns_in_val = float(df_calc.get('Return To Store In Amount', pd.Series([0])).sum())  # Items returned TO store
            returns_out_val = float(df_calc.get('Return-Qty Amount', pd.Series([0])).sum())  # Items returned to vendor
            consumption_val = float(df_calc.get('Indent Amount', pd.Series([0])).sum())
            ibt_out_val = float(df_calc.get('Ibt Out Amount', pd.Series([0])).sum())
            spoilage_val = float(df_calc.get('Spoilage Amount', pd.Series([0])).sum())
            closing_val = float(df_calc.get('Closing Amount', pd.Series([0])).sum())

            # Ensure spoilage is visible even if small - minimum 1% of total flow
            total_flow = opening_val + purchases_val + ibt_in_val + returns_in_val
            min_visible = total_flow * 0.01 if total_flow > 0 else 100
            spoilage_display = max(spoilage_val, min_visible) if spoilage_val > 0 else spoilage_val

            flow_data = {
                'Opening Stock': opening_val,
                '+ Purchases': purchases_val,
                '+ IBT Received': ibt_in_val,
                '+ Returns to Store': returns_in_val,  # Items returned TO store from work areas
                '- Returns to Vendor': -returns_out_val,  # Items returned to vendor
                '- Consumption/Usage': -consumption_val,
                '- IBT Sent Out': -ibt_out_val,
                '- Spoilage/Loss': -spoilage_display,
                'Closing Stock': closing_val
            }

            waterfall_labels = list(flow_data.keys())
            waterfall_values = [float(val) for val in flow_data.values()]

            charts.append({
                "id": "complete_inventory_flow",
                "title": "💰 Complete Inventory Movement Tracking - FINANCIAL OVERVIEW",
                "subtitle": "Strategic: Track every rupee from opening to closing stock",
                "type": "bar",
                "data": {
                    "labels": waterfall_labels,
                    "datasets": [{
                        "label": "Amount (₹)",
                        "data": waterfall_values,
                        "backgroundColor": [
                            '#4A90E2',  # Opening - Blue
                            '#7ED321',  # Purchases - Green
                            '#50E3C2',  # IBT In - Teal
                            '#B8E986',  # Returns to Store - Light Green (inward)
                            '#E74C3C',  # Returns to Vendor - Red (outward)
                            '#F5A623',  # Consumption - Orange
                            '#D0021B',  # IBT Out - Red
                            '#9013FE',  # Spoilage - Purple (distinct for visibility)
                            '#4A90E2'   # Closing - Blue
                        ],
                        "borderColor": '#FFFFFF',
                        "borderWidth": 2,
                        "borderRadius": 6
                    }]
                },
                "options": {
                    "responsive": True,
                    "maintainAspectRatio": False,
                    "plugins": {
                        "legend": {"display": False},
                        "title": {
                            "display": True,
                            "text": "Every movement tracked - No inventory unaccounted",
                            "font": {"size": 12, "style": "italic"},
                            "color": "#666"
                        },
                        "tooltip": {
                            "callbacks": {
                                "title": "function(context) { return context[0].label; }",
                                "label": "function(context) { var val = Math.abs(context.parsed.y); var actual = " + str(spoilage_val) + "; if(context.label.includes('Spoilage') && actual !== val) { return 'Actual Spoilage: ₹' + actual.toLocaleString() + ' (Enhanced for visibility)'; } return 'Amount: ₹' + val.toLocaleString(); }",
                                "afterLabel": "function(context) { if(context.parsed.y > 0) return '↗️ Inflow'; else if(context.parsed.y < 0) return '↘️ Outflow'; else return '📊 Stock Level'; }"
                            }
                        }
                    },
                    "scales": {
                        "x": {
                            "grid": {"display": False},
                            "ticks": {
                                "font": {"size": 10},
                                "maxRotation": 45
                            }
                        },
                        "y": {
                            "grid": {"color": "#E5E5E5"},
                            "ticks": {
                                "font": {"size": 10},
                                "callback": "function(value) { return '₹' + (Math.abs(value)/1000).toFixed(0) + 'K'; }"
                            }
                        }
                    }
                }
            })

        # 4. STRATEGIC: Top 10 Investment Items - FOCUS ON HIGH-VALUE ASSETS
        if 'Item Name' in df.columns and 'Closing Amount' in df.columns and 'Opening Amount' in df.columns:
            # Get top 10 items by current value for focused analysis
            top_items_analysis = df_calc.nlargest(10, 'Closing Amount')[
                ['Item Name', 'Item Code', 'Category', 'Closing Amount', 'Opening Amount', 'Period_Turnover']
            ].copy()

            if not top_items_analysis.empty:
                # Create simple info text for finance team
                high_value_info_text = """
                📊 Your Top 10 Most Expensive Inventory Items:

                • These items represent the biggest chunk of your inventory investment
                • Compare current vs opening stock to see if money is tied up or freed up
                • Focus on these items to control inventory costs and cash flow
                • Green bars show opening values, Blue bars show current values
                """

                item_names = [name[:20] + '...' if len(name) > 20 else name for name in top_items_analysis['Item Name'].tolist()]

                charts.append({
                    "id": "top_investment_items",
                    "title": "💰 Top 10 Investment Items - FOCUS ON HIGH-VALUE ASSETS",
                    "subtitle": "Strategic: Your highest-value inventory items requiring focused management",
                    "info": high_value_info_text,
                    "type": "bar",
                    "data": {
                        "labels": item_names,
                        "datasets": [
                            {
                                "label": "Opening Stock Value (₹)",
                                "data": [float(val) for val in top_items_analysis['Opening Amount'].round(2).tolist()],
                                "backgroundColor": '#27AE60',
                                "borderColor": '#229954',
                                "borderWidth": 1,
                                "borderRadius": 4
                            },
                            {
                                "label": "Current Stock Value (₹)",
                                "data": [float(val) for val in top_items_analysis['Closing Amount'].round(2).tolist()],
                                "backgroundColor": '#3498DB',
                                "borderColor": '#2980B9',
                                "borderWidth": 1,
                                "borderRadius": 4
                            }
                        ]
                    },
                    "options": {
                        "responsive": True,
                        "maintainAspectRatio": False,
                        "plugins": {
                            "legend": {
                                "position": "top",
                                "labels": {
                                    "usePointStyle": True,
                                    "padding": 20,
                                    "font": {"size": 12, "weight": "bold"},
                                    "color": "#2C3E50",
                                    "generateLabels": "function(chart) { var data = chart.data; return data.datasets.map(function(dataset, i) { return { text: dataset.label, fillStyle: dataset.backgroundColor, strokeStyle: dataset.borderColor, lineWidth: 2, pointStyle: 'circle' }; }); }"
                                }
                            },
                            "tooltip": {
                                "mode": "index",
                                "intersect": False,
                                "callbacks": {
                                    "title": "function(context) { return '" + str(top_items_analysis['Item Name'].tolist()) + "'[context[0].dataIndex]; }",
                                    "label": "function(context) { return context.dataset.label + ': ₹' + context.parsed.y.toLocaleString(); }",
                                    "afterBody": "function(context) { var opening = context.length > 0 ? context[0].parsed.y : 0; var current = context.length > 1 ? context[1].parsed.y : 0; var change = opening > 0 ? ((current - opening) / opening * 100).toFixed(1) : 'N/A'; var turnover = [" + str([float(t) for t in top_items_analysis['Period_Turnover'].tolist()]) + "][context[0].dataIndex]; var category = [" + str(top_items_analysis['Category'].tolist()) + "][context[0].dataIndex]; return 'Change: ' + (change !== 'N/A' ? (change > 0 ? '+' : '') + change + '%' : 'N/A') + ' | Turnover: ' + turnover.toFixed(2) + 'x | Category: ' + category; }"
                                }
                            }
                        },
                        "scales": {
                            "x": {
                                "grid": {"display": False},
                                "ticks": {
                                    "font": {"size": 9},
                                    "maxRotation": 45
                                }
                            },
                            "y": {
                                "beginAtZero": True,
                                "grid": {"color": "#E5E5E5"},
                                "title": {
                                    "display": True,
                                    "text": "Value (₹)",
                                    "font": {"size": 12}
                                },
                                "ticks": {
                                    "font": {"size": 10},
                                    "callback": "function(value) { return '₹' + (value/1000).toFixed(0) + 'K'; }"
                                }
                            }
                        }
                    }
                })


        # 4. Inventory Value by Category - Business Portfolio Overview
        if 'Category' in df.columns and 'Closing Amount' in df.columns:
            category_analysis = df_calc.groupby('Category').agg({
                'Closing Amount': 'sum',
                'Opening Amount': 'sum',
                'Item Code': 'count'
            }).sort_values('Closing Amount', ascending=False)

            if not category_analysis.empty:
                # Take top 8 categories for clarity
                top_categories = category_analysis.head(8)

                charts.append({
                    "id": "inventory_by_category",
                    "title": "Inventory Value by Category: Business Portfolio Overview",
                    "subtitle": "Understand which product categories drive your inventory investment",
                    "type": "bar",
                    "data": {
                        "labels": top_categories.index.tolist(),
                        "datasets": [
                            {
                                "label": "Current Stock Value (₹)",
                                "data": [float(val) for val in top_categories['Closing Amount'].round(2).tolist()],
                                "backgroundColor": '#4A90E2',
                                "borderColor": '#357ABD',
                                "borderWidth": 1,
                                "borderRadius": 4
                            },
                            {
                                "label": "Opening Stock Value (₹)",
                                "data": [float(val) for val in top_categories['Opening Amount'].round(2).tolist()],
                                "backgroundColor": '#7ED321',
                                "borderColor": '#5CB85C',
                                "borderWidth": 1,
                                "borderRadius": 4
                            }
                        ]
                    },
                    "options": {
                        "responsive": True,
                        "maintainAspectRatio": False,
                        "plugins": {
                            "legend": {
                                "position": "top",
                                "labels": {
                                    "usePointStyle": True,
                                    "padding": 20,
                                    "font": {"size": 12, "weight": "bold"},
                                    "color": "#2C3E50",
                                    "generateLabels": "function(chart) { var data = chart.data; return data.datasets.map(function(dataset, i) { return { text: dataset.label, fillStyle: dataset.backgroundColor, strokeStyle: dataset.borderColor, lineWidth: 2, pointStyle: 'circle' }; }); }"
                                }
                            },
                            "tooltip": {
                                "mode": "index",
                                "intersect": False,
                                "callbacks": {
                                    "title": "function(context) { return 'Category: ' + context[0].label; }",
                                    "label": "function(context) { return context.dataset.label + ': ₹' + context.parsed.y.toLocaleString(); }",
                                    "afterBody": "function(context) { var current = context[0].parsed.y; var opening = context[1] ? context[1].parsed.y : 0; var change = opening > 0 ? ((current - opening) / opening * 100).toFixed(1) : 'N/A'; return 'Change: ' + (change !== 'N/A' ? (change > 0 ? '+' : '') + change + '%' : 'N/A'); }"
                                }
                            }
                        },
                        "scales": {
                            "x": {
                                "grid": {"display": False},
                                "ticks": {"font": {"size": 10}}
                            },
                            "y": {
                                "beginAtZero": True,
                                "grid": {"color": "#E5E5E5"},
                                "title": {
                                    "display": True,
                                    "text": "Value (₹)",
                                    "font": {"size": 12}
                                },
                                "ticks": {
                                    "font": {"size": 10},
                                    "callback": "function(value) { return '₹' + (value/1000).toFixed(0) + 'K'; }"
                                }
                            }
                        }
                    }
                })

        # 5. STRATEGIC: Smart Movement Analysis - OPERATIONAL EFFICIENCY INSIGHTS
        if 'Category' in df.columns:
            movement_analysis = df_calc.groupby('Category').agg({
                'Purchase Amount': 'sum',
                'Ibt In Amount': 'sum',
                'Return To Store In Amount': 'sum',  # Returns TO store (inward)
                'Return-Qty Amount': 'sum',  # Returns to vendor (outward)
                'Indent Amount': 'sum',
                'Ibt Out Amount': 'sum',
                'Spoilage Amount': 'sum'
            }).fillna(0)

            # Calculate smart movement metrics
            movement_analysis['Total_Inflow'] = (
                movement_analysis['Purchase Amount'] +
                movement_analysis['Ibt In Amount'] +
                movement_analysis['Return To Store In Amount']
            )
            movement_analysis['Total_Outflow'] = (
                movement_analysis['Return-Qty Amount'] +
                movement_analysis['Indent Amount'] +
                movement_analysis['Ibt Out Amount'] +
                movement_analysis['Spoilage Amount']
            )
            movement_analysis['Net_Balance'] = movement_analysis['Total_Inflow'] - movement_analysis['Total_Outflow']
            movement_analysis['Activity_Level'] = movement_analysis['Total_Inflow'] + movement_analysis['Total_Outflow']

            if not movement_analysis.empty:
                # Take top 6 most active categories
                top_active_categories = movement_analysis.nlargest(6, 'Activity_Level')

                # Create movement insights info
                movement_info_text = """
                🔄 Smart Movement Analysis - Understand Your Inventory Flow Patterns:

                📊 What This Shows:
                • Green Bars: Money flowing INTO inventory (purchases, transfers in, returns from work areas)
                • Red Bars: Money flowing OUT of inventory (consumption, transfers out, returns to vendors, spoilage)
                • Net Balance: Whether category is building up stock (positive) or consuming (negative)

                💡 Smart Insights:
                • High Activity = High business importance (focus management attention here)
                • Balanced Flow = Healthy inventory management
                • Heavy Inflow = Stock building (check if intentional)
                • Heavy Outflow = High consumption (ensure adequate supply)

                🎯 Action Guide:
                • High Activity + Balanced: Excellent management ✅
                • High Activity + Imbalanced: Review ordering patterns ⚠️
                • Low Activity: Consider if category is needed 🤔
                """

                categories = top_active_categories.index.tolist()
                inflow_data = [float(val) for val in top_active_categories['Total_Inflow'].tolist()]
                outflow_data = [float(val) for val in top_active_categories['Total_Outflow'].tolist()]
                net_balances = [float(val) for val in top_active_categories['Net_Balance'].tolist()]

                charts.append({
                    "id": "smart_movement_analysis",
                    "title": "💰 Smart Movement Analysis - OPERATIONAL EFFICIENCY",
                    "subtitle": "Strategic: Category activity and flow balance for operational optimization",
                    "info": movement_info_text,
                    "type": "bar",
                    "data": {
                        "labels": categories,
                        "datasets": [
                            {
                                "label": "💰 Money IN (Purchases + Transfers + Returns)",
                                "data": inflow_data,
                                "backgroundColor": '#27AE60',
                                "borderColor": '#229954',
                                "borderWidth": 2,
                                "borderRadius": 6
                            },
                            {
                                "label": "💸 Money OUT (Usage + Transfers + Returns + Spoilage)",
                                "data": outflow_data,
                                "backgroundColor": '#E74C3C',
                                "borderColor": '#C0392B',
                                "borderWidth": 2,
                                "borderRadius": 6
                            }
                        ]
                    },
                    "options": {
                        "responsive": True,
                        "maintainAspectRatio": False,
                        "plugins": {
                            "legend": {
                                "position": "top",
                                "labels": {
                                    "usePointStyle": True,
                                    "padding": 20,
                                    "font": {"size": 12, "weight": "bold"}
                                }
                            },
                            "title": {
                                "display": True,
                                "text": "💡 Look for balance: Healthy categories have proportional inflow and outflow",
                                "font": {"size": 11, "style": "italic"},
                                "color": "#666"
                            },
                            "tooltip": {
                                "mode": "index",
                                "intersect": False,
                                "callbacks": {
                                    "title": "function(context) { return '📦 Category: ' + context[0].label; }",
                                    "label": "function(context) { return context.dataset.label + ': ₹' + context.parsed.y.toLocaleString(); }",
                                    "afterBody": "function(context) { var netBalances = " + str(net_balances) + "; var net = netBalances[context[0].dataIndex]; var activity = context[0].parsed.y + (context[1] ? context[1].parsed.y : 0); var status = Math.abs(net) < activity * 0.2 ? '✅ Well Balanced' : net > 0 ? '📈 Building Stock' : '📉 High Consumption'; return ['Net Balance: ₹' + net.toLocaleString(), 'Status: ' + status, 'Activity Level: ₹' + activity.toLocaleString()]; }"
                                }
                            }
                        },
                        "scales": {
                            "x": {
                                "grid": {"display": False},
                                "ticks": {"font": {"size": 10}}
                            },
                            "y": {
                                "beginAtZero": True,
                                "grid": {"color": "#E5E5E5"},
                                "title": {
                                    "display": True,
                                    "text": "Amount (₹)",
                                    "font": {"size": 12}
                                },
                                "ticks": {
                                    "font": {"size": 10},
                                    "callback": "function(value) { return '₹' + (value/1000).toFixed(0) + 'K'; }"
                                }
                            }
                        }
                    }
                })

        # 6. IBT (Inter-Branch Transfer) Analysis - Track inventory movements between locations
        if all(col in df.columns for col in ['Ibt In Amount', 'Ibt Out Amount', 'Location']):
            # Calculate IBT activity by location
            ibt_analysis = df_calc.groupby('Location').agg({
                'Ibt In Amount': 'sum',
                'Ibt Out Amount': 'sum',
                'Item Code': 'count'
            }).round(2)

            # Filter locations with IBT activity
            active_ibt_locations = ibt_analysis[(ibt_analysis['Ibt In Amount'] > 0) | (ibt_analysis['Ibt Out Amount'] > 0)]

            if not active_ibt_locations.empty:
                locations = active_ibt_locations.index.tolist()
                ibt_in_values = [float(val) for val in active_ibt_locations['Ibt In Amount'].tolist()]
                ibt_out_values = [float(val) for val in active_ibt_locations['Ibt Out Amount'].tolist()]

                # Create info text for IBT analysis
                ibt_info_text = """
                🔄 Inter-Branch Transfer (IBT) Analysis shows inventory movement between your locations:

                📊 What This Shows:
                • IBT In: Inventory received from other branches (inward movement)
                • IBT Out: Inventory sent to other branches (outward movement)
                • Net Transfer: Overall transfer balance per location

                💡 Business Insights:
                • High IBT In: Location receives significant inventory from other branches
                • High IBT Out: Location supplies inventory to other branches
                • Balanced transfers indicate good inter-branch coordination
                • Imbalanced transfers may indicate supply chain optimization opportunities

                🎯 Management Actions:
                • Monitor transfer patterns to optimize inventory distribution
                • Identify hub locations vs. receiving locations
                • Ensure transfer costs are justified by operational benefits
                • Review transfer frequency and batch sizes for efficiency
                """

                charts.append({
                    "id": "ibt_analysis",
                    "title": "Inter-Branch Transfer Analysis: Inventory Movement Between Locations",
                    "subtitle": "Track how inventory flows between your restaurant locations",
                    "info": ibt_info_text,
                    "type": "bar",
                    "data": {
                        "labels": locations,
                        "datasets": [
                            {
                                "label": "IBT In (Received) ₹",
                                "data": ibt_in_values,
                                "backgroundColor": '#27AE60',
                                "borderColor": '#229954',
                                "borderWidth": 1,
                                "borderRadius": 4
                            },
                            {
                                "label": "IBT Out (Sent) ₹",
                                "data": ibt_out_values,
                                "backgroundColor": '#E74C3C',
                                "borderColor": '#C0392B',
                                "borderWidth": 1,
                                "borderRadius": 4
                            }
                        ]
                    },
                    "options": {
                        "responsive": True,
                        "maintainAspectRatio": False,
                        "plugins": {
                            "legend": {
                                "position": "top",
                                "labels": {
                                    "usePointStyle": True,
                                    "padding": 20,
                                    "font": {"size": 12, "weight": "bold"}
                                }
                            },
                            "title": {
                                "display": True,
                                "text": "🟢 Green = Inventory Received | 🔴 Red = Inventory Sent",
                                "font": {"size": 11, "style": "italic"},
                                "color": "#666"
                            },
                            "tooltip": {
                                "mode": "index",
                                "intersect": False,
                                "callbacks": {
                                    "title": "function(context) { return 'Location: ' + context[0].label; }",
                                    "label": "function(context) { return context.dataset.label + ': ₹' + context.parsed.y.toLocaleString(); }",
                                    "afterBody": "function(context) { var received = context[0] ? context[0].parsed.y : 0; var sent = context[1] ? context[1].parsed.y : 0; var net = received - sent; return 'Net Transfer: ₹' + net.toLocaleString() + (net > 0 ? ' (Net Receiver)' : net < 0 ? ' (Net Sender)' : ' (Balanced)'); }"
                                }
                            }
                        },
                        "scales": {
                            "x": {
                                "grid": {"display": False},
                                "ticks": {"font": {"size": 10}}
                            },
                            "y": {
                                "beginAtZero": True,
                                "grid": {"color": "#E5E5E5"},
                                "title": {
                                    "display": True,
                                    "text": "Transfer Amount (₹)",
                                    "font": {"size": 12}
                                },
                                "ticks": {
                                    "font": {"size": 10},
                                    "callback": "function(value) { return '₹' + (value/1000).toFixed(0) + 'K'; }"
                                }
                            }
                        }
                    }
                })

        # 📈 TIER 3: PERFORMANCE MONITORING (Important but Secondary)

        # 7. PERFORMANCE: Location Performance Dashboard - MULTI-LOCATION COMPARISON
        if 'Location' in df.columns:
            location_performance = df_calc.groupby('Location').agg({
                'Closing Amount': 'sum',
                'Opening Amount': 'sum',
                'Period_Turnover': 'mean',
                'Spoilage Amount': 'sum',
                'Item Code': 'count'
            }).round(2)

            if not location_performance.empty and len(location_performance) > 1:
                # Calculate meaningful metrics for each location
                location_metrics = []
                for location, row in location_performance.iterrows():
                    growth_rate = ((row['Closing Amount'] - row['Opening Amount']) / row['Opening Amount'] * 100) if row['Opening Amount'] > 0 else 0
                    spoilage_rate = (row['Spoilage Amount'] / row['Closing Amount'] * 100) if row['Closing Amount'] > 0 else 0

                    location_metrics.append({
                        'location': location,
                        'stock_value': float(row['Closing Amount']),
                        'growth_rate': float(growth_rate),
                        'turnover_rate': float(row['Period_Turnover']),
                        'spoilage_rate': float(spoilage_rate),
                        'item_count': int(row['Item Code'])
                    })

                # Sort by stock value for better presentation
                location_metrics.sort(key=lambda x: x['stock_value'], reverse=True)

                locations = [loc['location'] for loc in location_metrics]
                stock_values = [loc['stock_value'] for loc in location_metrics]
                turnover_rates = [loc['turnover_rate'] for loc in location_metrics]

                # Create location performance info
                location_info_text = """
                🏢 Location Performance Dashboard - Compare Your Restaurant Locations:

                📊 Key Metrics Explained:
                • Stock Value: Total inventory investment per location
                • Turnover Rate: How efficiently each location converts inventory to sales
                • Growth Rate: Stock value change from opening to closing
                • Spoilage Rate: Percentage of inventory lost to waste

                🎯 What to Look For:
                • High stock value + High turnover = Excellent performance
                • High stock value + Low turnover = Money tied up, needs attention
                • High spoilage rate = Process improvement needed
                • Consistent performance across locations = Good standardization

                💡 Management Actions:
                • Best performers: Study and replicate their practices
                • Poor performers: Investigate causes and implement improvements
                • Optimize inventory allocation based on location performance
                """

                charts.append({
                    "id": "location_performance_dashboard",
                    "title": "📈 Location Performance - MULTI-LOCATION COMPARISON",
                    "subtitle": "Performance: Compare inventory investment and turnover efficiency across locations",
                    "info": location_info_text,
                    "type": "bar",
                    "data": {
                        "labels": locations,
                        "datasets": [
                            {
                                "label": "📊 Stock Value (₹)",
                                "data": stock_values,
                                "backgroundColor": '#3498DB',
                                "borderColor": '#2980B9',
                                "borderWidth": 2,
                                "borderRadius": 6,
                                "yAxisID": 'y'
                            },
                            {
                                "label": "🔄 Turnover Rate (x)",
                                "data": turnover_rates,
                                "backgroundColor": '#E74C3C',
                                "borderColor": '#C0392B',
                                "borderWidth": 2,
                                "borderRadius": 6,
                                "yAxisID": 'y1',
                                "type": 'line',
                                "fill": False,
                                "tension": 0.4,
                                "pointRadius": 6,
                                "pointHoverRadius": 8
                            }
                        ]
                    },
                    "options": {
                        "responsive": True,
                        "maintainAspectRatio": False,
                        "interaction": {
                            "mode": "index",
                            "intersect": False
                        },
                        "plugins": {
                            "legend": {
                                "position": "top",
                                "labels": {
                                    "usePointStyle": True,
                                    "padding": 20,
                                    "font": {"size": 12, "weight": "bold"}
                                }
                            },
                            "title": {
                                "display": True,
                                "text": "💡 Best locations: High stock value + High turnover rate",
                                "font": {"size": 11, "style": "italic"},
                                "color": "#666"
                            },
                            "tooltip": {
                                "callbacks": {
                                    "title": "function(context) { return '🏢 Location: ' + context[0].label; }",
                                    "label": "function(context) { var metrics = " + str(location_metrics) + "; var metric = metrics[context.dataIndex]; if(context.datasetIndex === 0) { return '📊 Stock Value: ₹' + context.parsed.y.toLocaleString(); } else { return '🔄 Turnover Rate: ' + context.parsed.y.toFixed(2) + 'x'; } }",
                                    "afterBody": "function(context) { var metrics = " + str(location_metrics) + "; var metric = metrics[context[0].dataIndex]; var performance = metric.turnover_rate >= 1.5 ? '🟢 Excellent' : metric.turnover_rate >= 1.0 ? '🟡 Good' : '🔴 Needs Improvement'; return ['Growth Rate: ' + metric.growth_rate.toFixed(1) + '%', 'Spoilage Rate: ' + metric.spoilage_rate.toFixed(1) + '%', 'Items: ' + metric.item_count, 'Performance: ' + performance]; }"
                                }
                            }
                        },
                        "scales": {
                            "x": {
                                "grid": {"display": False},
                                "ticks": {"font": {"size": 10}}
                            },
                            "y": {
                                "type": "linear",
                                "display": True,
                                "position": "left",
                                "beginAtZero": True,
                                "grid": {"color": "#E5E5E5"},
                                "title": {
                                    "display": True,
                                    "text": "Stock Value (₹)",
                                    "font": {"size": 12},
                                    "color": "#3498DB"
                                },
                                "ticks": {
                                    "font": {"size": 10},
                                    "callback": "function(value) { return '₹' + (value/1000).toFixed(0) + 'K'; }"
                                }
                            },
                            "y1": {
                                "type": "linear",
                                "display": True,
                                "position": "right",
                                "beginAtZero": True,
                                "grid": {"drawOnChartArea": False},
                                "title": {
                                    "display": True,
                                    "text": "Turnover Rate (x)",
                                    "font": {"size": 12},
                                    "color": "#E74C3C"
                                },
                                "ticks": {
                                    "font": {"size": 10},
                                    "callback": "function(value) { return value.toFixed(1) + 'x'; }"
                                }
                            }
                        }
                    }
                })



        # === SPECIALIZED ANALYSIS SECTION ===

        # 10. Work Area Consumption Analysis (Critical operational insight)
        if 'Indent Amount' in df.columns and any('work' in col.lower() for col in df.columns):
            # Get work area related columns
            work_area_cols = [col for col in df.columns if 'work' in col.lower() and 'amount' in col.lower()]

            if work_area_cols:
                work_area_consumption_info = """
                🏭 Work Area Consumption Analysis shows how different operational areas use inventory:

                📊 Why This Matters:
                • Identifies which work areas consume the most inventory value
                • Helps optimize inventory allocation across operations
                • Reveals efficiency differences between work areas
                • Critical for cost control and operational planning

                💡 Key Insights:
                • High Consumption Areas: May need dedicated inventory management
                • Low Consumption Areas: Check if underutilized or efficient
                • Consumption Patterns: Help predict future inventory needs
                • Cost Centers: Identify areas driving inventory costs

                🎯 Management Actions:
                • High consumers: Ensure adequate supply, monitor waste
                • Efficient areas: Study best practices for replication
                • Irregular patterns: Investigate operational changes needed
                """

                # Aggregate work area consumption data
                work_area_data = {}
                for col in work_area_cols:
                    area_name = col.replace(' Amount', '').replace('Total(incl.tax,etc)', '').strip()
                    if area_name and area_name != 'All Work Area Stock':
                        work_area_data[area_name] = float(df_calc.get(col, pd.Series([0])).sum())

                if work_area_data:
                    # Sort by consumption value
                    sorted_areas = dict(sorted(work_area_data.items(), key=lambda x: x[1], reverse=True))
                    top_work_areas = dict(list(sorted_areas.items())[:8])  # Top 8 work areas

                    charts.append({
                        "id": "work_area_consumption_analysis",
                        "title": "Work Area Consumption Analysis: Operational Efficiency Insights",
                        "subtitle": "Track inventory consumption across different operational areas",
                        "info": work_area_consumption_info,
                        "type": "bar",
                        "data": {
                            "labels": list(top_work_areas.keys()),
                            "datasets": [{
                                "label": "💰 Total Consumption Value (₹)",
                                "data": list(top_work_areas.values()),
                                "backgroundColor": '#E67E22',
                                "borderColor": '#D35400',
                                "borderWidth": 2,
                                "borderRadius": 6
                            }]
                        },
                        "options": {
                            "responsive": True,
                            "maintainAspectRatio": False,
                            "plugins": {
                                "legend": {
                                    "display": True,
                                    "position": "top",
                                    "labels": {
                                        "usePointStyle": True,
                                        "padding": 20,
                                        "font": {"size": 12, "weight": "bold"},
                                        "color": "#2C3E50"
                                    }
                                },
                                "title": {
                                    "display": True,
                                    "text": "🎯 Focus on high-consumption areas for cost optimization",
                                    "font": {"size": 11, "style": "italic"},
                                    "color": "#7F8C8D"
                                },
                                "tooltip": {
                                    "callbacks": {
                                        "title": "function(context) { return '🏭 Work Area: ' + context[0].label; }",
                                        "label": "function(context) { var total = context.dataset.data.reduce((a, b) => a + b, 0); var percentage = ((context.parsed.y / total) * 100).toFixed(1); return '💰 Consumption: ₹' + context.parsed.y.toLocaleString() + ' (' + percentage + '% of total)'; }",
                                        "afterLabel": "function(context) { var value = context.parsed.y; if(value > 100000) return '🔴 High consumption - Monitor closely'; else if(value > 50000) return '🟡 Moderate consumption - Regular review'; else return '🟢 Low consumption - Efficient operation'; }"
                                    }
                                }
                            },
                            "scales": {
                                "x": {
                                    "grid": {"display": False},
                                    "ticks": {
                                        "font": {"size": 10, "weight": "bold"},
                                        "color": "#2C3E50",
                                        "maxRotation": 45
                                    },
                                    "title": {
                                        "display": True,
                                        "text": "🏭 Work Areas",
                                        "font": {"size": 12, "weight": "bold"},
                                        "color": "#2C3E50"
                                    }
                                },
                                "y": {
                                    "beginAtZero": True,
                                    "grid": {"color": "#ECF0F1"},
                                    "title": {
                                        "display": True,
                                        "text": "💰 Consumption Value (₹)",
                                        "font": {"size": 12, "weight": "bold"},
                                        "color": "#2C3E50"
                                    },
                                    "ticks": {
                                        "font": {"size": 10, "weight": "bold"},
                                        "color": "#2C3E50",
                                        "callback": "function(value) { return '₹' + (value/1000).toFixed(0) + 'K'; }"
                                    }
                                }
                            }
                        }
                    })



        return {
            "success": True,
            "charts": charts,
            "summary_items": summary_items
        }

    except Exception as e:
        print(f"Error generating inventory dashboard: {str(e)}")
        import traceback
        traceback.print_exc()
        return {
            "success": False,
            "error": str(e),
            "charts": [],
            "summary_items": []
        }


def smart_ask_dashboard(df: pd.DataFrame, user_query: str = "", use_default_charts: bool = True) -> dict:
    openai_api_key = os.getenv("OPENAI_API_KEY")
    llm = OpenAI(api_key=openai_api_key, model="gpt-4")

    # Get global chart colors for consistency
    global_colors = get_global_chart_colors()
    colors_str = str(global_colors)

    columns = list(df.columns)
    
    if use_default_charts or not user_query:
        analysis_prompt = f"""
            You are a data analyst generating a dashboard for an Indian restaurant business.

            Dataset has {df.shape[0]} rows and {df.shape[1]} columns.
            Columns: {columns}

            
            - **Purchase Overview Section**: A dedicated section showcasing purchase metrics.
            - **Total Purchase Cost**: Bar chart displaying the total cost of purchases.
            - **Purchase Cost by Location**, Supplier: Grouped bar chart or stacked bar chart illustrating spending patterns.
            - **Cost Trends**: Line chart or area chart depicting cost trends over time.
            - **Overall Purchase Value Comparison**: Bar chart or bullet graph for holistic spending analysis.
            - **Cost per Item**: Line chart or bar chart showing the average cost per item.
            - **Purchase Order Accuracy**: Area chart or line chart indicating the accuracy of purchase orders.
            - **Purchase by Category**: Stacked bar chart or table showcasing purchase distribution by categories.
            - **Top 15 Purchase Items**: Table or bar chart displaying the top 15 purchased items.

            Rules:
            - Use only actual data values.
            - For date-wise insights, use columns containing 'Based on'.
            - No assumptions or fabrication.

            Your task:
            - Extract factual business metrics.
            - Identify trends, totals, comparisons.
            - Keep insights concise.
        """
    else:
        analysis_prompt = f"""
            You are a data analyst answering this business question for an Indian restaurant:

            "{user_query}"

            Dataset has {df.shape[0]} rows and {df.shape[1]} columns.
            Columns: {columns}

            Rules:
            - Use only actual data.
            - Use only date columns containing 'Based on'.
            - No guessing or fabrication.
            - Be concise with numeric summaries or patterns.
        """
    
    query_engine = PandasQueryEngine(df=df, llm=llm, verbose=False, synthesize_response=True)
    analysis_response = query_engine.query(analysis_prompt)
    analysis_text = str(analysis_response).strip()

    print("Analysis text:", analysis_text)

    formatting_prompt = f"""
        Convert the following analysis text into a single valid JSON matching this schema:

        {{
        "charts": [
            {{
            "id": "string",
            "title": "string",
            "type": "string",
            "data": {{
                "labels": ["string"],
                "datasets": [
                {{
                    "label": "string",
                    "data": [float],
                    "backgroundColor": ["string"],
                    "borderColor": ["string"]
                }}
                ]
            }}
            }}
        ],
        "summary_items": [
            {{
            "icon": "string",
            "value": "string",
            "label": "string",
            "data_type": "string"
            }}
        ]
        }}

        Use only facts from the text below — no extra info or calculations.
        Match label and data array lengths exactly.
        Use these colors in order for charts: {colors_str}
        Output ONLY JSON — no explanations or text.

        IMPORTANT:
        - Summary items should be concise top-level business KPIs.
        - Do NOT repeat any data already shown in charts within summary items.

        Analysis text:
        \"\"\"
        {analysis_text}
        \"\"\"
    """

    json_response = llm.complete(prompt=formatting_prompt).text.strip()
    dashboard = DashboardResponse.parse_raw(json_response)

    return {
        "success": True,
        "charts": dashboard.charts,
        "summary_items": dashboard.summary_items
    }
