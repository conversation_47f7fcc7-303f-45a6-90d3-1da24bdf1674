{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { NgChartsModule } from 'ng2-charts';\nimport { NgxMatSelectSearchModule } from 'ngx-mat-select-search';\nimport { FormControl, ReactiveFormsModule, FormsModule } from '@angular/forms';\nimport { Subject, takeUntil, debounceTime, distinctUntilChanged } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/smart-dashboard.service\";\nimport * as i2 from \"../../services/auth.service\";\nimport * as i3 from \"../../services/share-data.service\";\nimport * as i4 from \"../../services/dashboard-config.service\";\nimport * as i5 from \"../../services/chart-renderer.service\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/material/card\";\nimport * as i8 from \"@angular/material/button\";\nimport * as i9 from \"@angular/material/icon\";\nimport * as i10 from \"@angular/material/form-field\";\nimport * as i11 from \"@angular/material/select\";\nimport * as i12 from \"@angular/material/core\";\nimport * as i13 from \"@angular/material/input\";\nimport * as i14 from \"@angular/material/datepicker\";\nimport * as i15 from \"@angular/material/progress-spinner\";\nimport * as i16 from \"ng2-charts\";\nimport * as i17 from \"ngx-mat-select-search\";\nimport * as i18 from \"@angular/forms\";\nfunction SmartDashboardComponent_mat_option_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 39);\n    i0.ɵɵtext(1, \"Loading...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SmartDashboardComponent_mat_option_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 40);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dashboardType_r9 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", dashboardType_r9.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", dashboardType_r9.label, \" \");\n  }\n}\nfunction SmartDashboardComponent_mat_option_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 40);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const branch_r10 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", branch_r10.restaurantIdOld);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", branch_r10.branchName, \" \");\n  }\n}\nfunction SmartDashboardComponent_div_29_mat_option_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 39);\n    i0.ɵɵtext(1, \"Loading...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SmartDashboardComponent_div_29_mat_option_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 40);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const baseDateOption_r13 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", baseDateOption_r13.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", baseDateOption_r13.label, \" \");\n  }\n}\nfunction SmartDashboardComponent_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"h4\", 12)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"event\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Base Date \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"mat-form-field\", 13)(6, \"mat-label\");\n    i0.ɵɵtext(7, \"Select base date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"mat-select\", 41);\n    i0.ɵɵtemplate(9, SmartDashboardComponent_div_29_mat_option_9_Template, 2, 0, \"mat-option\", 6);\n    i0.ɵɵtemplate(10, SmartDashboardComponent_div_29_mat_option_10_Template, 2, 2, \"mat-option\", 7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"formControl\", ctx_r3.baseDateCtrl)(\"disabled\", !ctx_r3.isConfigLoaded);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.isConfigLoaded);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.baseDateOptions);\n  }\n}\nfunction SmartDashboardComponent_div_79_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵelement(1, \"mat-spinner\", 43);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading dashboard data...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SmartDashboardComponent_div_80_div_1_mat_card_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 49)(1, \"mat-card-content\")(2, \"div\", 50)(3, \"div\", 51)(4, \"mat-icon\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 52)(7, \"div\", 53);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 54);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const card_r17 = ctx.$implicit;\n    i0.ɵɵstyleProp(\"border-left-color\", card_r17.color);\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"color\", card_r17.color);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(card_r17.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(card_r17.value);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(card_r17.label);\n  }\n}\nfunction SmartDashboardComponent_div_80_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵtemplate(1, SmartDashboardComponent_div_80_div_1_mat_card_1_Template, 11, 7, \"mat-card\", 48);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r14.summaryCards);\n  }\n}\nfunction SmartDashboardComponent_div_80_div_2_mat_card_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 57)(1, \"mat-card-header\")(2, \"mat-card-title\", 58);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"mat-card-content\")(5, \"div\", 59);\n    i0.ɵɵelement(6, \"canvas\", 60);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const chart_r19 = ctx.$implicit;\n    const ctx_r18 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r18.getChartCssClass(chart_r19));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(chart_r19.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵattribute(\"data-chart-type\", chart_r19.type);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"type\", ctx_r18.getChartType(chart_r19))(\"data\", ctx_r18.getChartData(chart_r19))(\"options\", ctx_r18.getChartOptions(chart_r19));\n  }\n}\nfunction SmartDashboardComponent_div_80_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55);\n    i0.ɵɵtemplate(1, SmartDashboardComponent_div_80_div_2_mat_card_1_Template, 7, 6, \"mat-card\", 56);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r15 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r15.charts);\n  }\n}\nfunction SmartDashboardComponent_div_80_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44);\n    i0.ɵɵtemplate(1, SmartDashboardComponent_div_80_div_1_Template, 2, 1, \"div\", 45);\n    i0.ɵɵtemplate(2, SmartDashboardComponent_div_80_div_2_Template, 2, 1, \"div\", 46);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.summaryCards.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.charts.length > 0);\n  }\n}\nfunction SmartDashboardComponent_div_81_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 61)(1, \"mat-icon\", 62);\n    i0.ɵɵtext(2, \"analytics\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"No Data Available\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"Please select a location and date range to view dashboard data.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 63);\n    i0.ɵɵlistener(\"click\", function SmartDashboardComponent_div_81_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r21 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r21.searchDashboard());\n    });\n    i0.ɵɵelementStart(8, \"mat-icon\");\n    i0.ɵɵtext(9, \"refresh\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10, \" Refresh Data \");\n    i0.ɵɵelementEnd()();\n  }\n}\nclass SmartDashboardComponent {\n  constructor(smartDashboardService, authService, shareDataService, configService, chartRenderer, cdr) {\n    this.smartDashboardService = smartDashboardService;\n    this.authService = authService;\n    this.shareDataService = shareDataService;\n    this.configService = configService;\n    this.chartRenderer = chartRenderer;\n    this.cdr = cdr;\n    this.destroy$ = new Subject();\n    this.branches = [];\n    this.filteredBranches = [];\n    this.selectedLocations = [];\n    // Form controls\n    this.locationFilterCtrl = new FormControl('');\n    this.startDate = new FormControl();\n    this.endDate = new FormControl();\n    this.searchQuery = new FormControl('');\n    this.baseDateCtrl = new FormControl();\n    this.selectedDashboard = '';\n    // Dashboard data\n    this.summaryCards = [];\n    this.charts = [];\n    this.isLoading = false;\n    this.isConfigLoaded = false;\n    // Dynamic configuration data\n    this.dashboardTypes = [];\n    this.baseDateOptions = [];\n    this.user = this.authService.getCurrentUser();\n    this.initializeConfig();\n  }\n  initializeConfig() {\n    // Load dashboard configuration on component initialization\n    this.configService.loadConfig().subscribe({\n      next: response => {\n        if (response.status === 'success') {\n          this.configService.setConfig(response.data);\n          this.setupDynamicConfigurations(response.data);\n        } else {\n          this.setupDefaultConfigurations();\n        }\n        this.isConfigLoaded = true;\n        this.cdr.detectChanges();\n      },\n      error: () => {\n        this.setupDefaultConfigurations();\n        this.isConfigLoaded = true;\n        this.cdr.detectChanges();\n      }\n    });\n  }\n  setupDynamicConfigurations(config) {\n    // Set dashboard types\n    this.dashboardTypes = config.dashboard_types || [];\n    // Set base date options\n    this.baseDateOptions = config.base_date_options || [];\n    // Set default values from UI config\n    const uiConfig = config.ui_config || {};\n    // Set default form values\n    this.selectedDashboard = uiConfig.default_dashboard_type || 'purchase';\n    this.baseDateCtrl.setValue(uiConfig.default_base_date || 'deliveryDate');\n    // Set date range based on dashboard type\n    this.setDefaultDateRange();\n    // Configuration is set - user can now click Search to load data\n    // No automatic data loading\n  }\n\n  setupDefaultConfigurations() {\n    // Fallback configurations if backend fails\n    this.dashboardTypes = [{\n      value: 'purchase',\n      label: 'Purchase Dashboard'\n    }, {\n      value: 'inventory',\n      label: 'Inventory Dashboard'\n    }];\n    this.baseDateOptions = [{\n      value: 'deliveryDate',\n      label: 'Delivery Date'\n    }, {\n      value: 'orderDate',\n      label: 'Order Date'\n    }, {\n      value: 'createdDate',\n      label: 'Created Date'\n    }];\n    this.selectedDashboard = 'inventory';\n    this.baseDateCtrl.setValue('deliveryDate');\n    // Set date range based on dashboard type\n    this.setDefaultDateRange();\n    // Fallback configuration is set - user can now click Search to load data\n    // No automatic data loading\n  }\n\n  ngOnInit() {\n    this.initializeFilters();\n    this.loadBranches();\n    // Don't load dashboard data immediately - wait for config to load\n    // Dashboard data will be loaded after config is ready\n  }\n\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  initializeFilters() {\n    // Location filter\n    this.locationFilterCtrl.valueChanges.pipe(debounceTime(200), distinctUntilChanged(), takeUntil(this.destroy$)).subscribe(value => {\n      this.filterBranches(value || '');\n    });\n  }\n  loadBranches() {\n    this.shareDataService.selectedBranchesSource.pipe(takeUntil(this.destroy$)).subscribe(data => {\n      this.branches = data || [];\n      this.filteredBranches = [...this.branches];\n      // Select all branches by default\n      this.selectedLocations = this.branches.map(branch => branch.restaurantIdOld);\n    });\n  }\n  filterBranches(searchTerm) {\n    if (!searchTerm) {\n      this.filteredBranches = [...this.branches];\n    } else {\n      const normalizedSearchTerm = searchTerm.toLowerCase().replace(/\\s/g, '');\n      this.filteredBranches = this.branches.filter(branch => branch.branchName.toLowerCase().replace(/\\s/g, '').includes(normalizedSearchTerm));\n    }\n  }\n  loadDashboardData() {\n    if (!this.startDate.value || !this.endDate.value || !this.selectedDashboard) {\n      return;\n    }\n    this.isLoading = true;\n    const filters = {\n      locations: this.selectedLocations || [],\n      startDate: this.formatDate(this.startDate.value),\n      endDate: this.formatDate(this.endDate.value),\n      baseDate: this.baseDateCtrl.value || 'deliveryDate'\n    };\n    const request = {\n      tenant_id: this.user.tenantId,\n      filters: filters,\n      user_query: '',\n      use_default_charts: true,\n      dashboard_type: this.selectedDashboard\n    };\n    this.smartDashboardService.getSmartDashboardData(request).pipe(takeUntil(this.destroy$)).subscribe({\n      next: response => {\n        if (response.status === 'success') {\n          this.processDashboardData(response.data);\n        } else {\n          this.clearDashboardData();\n        }\n        this.isLoading = false;\n        this.cdr.detectChanges();\n      },\n      error: () => {\n        this.clearDashboardData();\n        this.isLoading = false;\n        this.cdr.detectChanges();\n      }\n    });\n  }\n  clearDashboardData() {\n    this.summaryCards = [];\n    this.charts = [];\n  }\n  processDashboardData(data) {\n    // Process summary cards using config service\n    this.summaryCards = data.summary_items?.map(item => ({\n      icon: item.icon || this.configService.getSummaryCardIcon(item.data_type),\n      value: item.value,\n      label: item.label,\n      color: this.configService.getSummaryCardColor(item.data_type),\n      data_type: item.data_type\n    })) || [];\n    // Process charts using chart renderer service\n    this.charts = data.charts?.map(chart => this.chartRenderer.processChart(chart)) || [];\n  }\n  formatDate(date) {\n    // Fix the date offset issue by using local date components\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const day = String(date.getDate()).padStart(2, '0');\n    return `${year}-${month}-${day}`;\n  }\n  setDefaultDateRange() {\n    const today = new Date();\n    if (this.selectedDashboard === 'inventory') {\n      // For inventory dashboard: current month start to current date\n      const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);\n      this.startDate.setValue(startOfMonth);\n      this.endDate.setValue(today);\n    } else {\n      // For purchase dashboard: last 30 days (existing behavior)\n      const thirtyDaysAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);\n      this.startDate.setValue(thirtyDaysAgo);\n      this.endDate.setValue(today);\n    }\n  }\n  onLocationChange() {\n    // No automatic API call - user must click Search button\n  }\n  onDateChange() {\n    // No automatic API call - user must click Search button\n  }\n  onDashboardChange() {\n    // Update date range when dashboard type changes\n    this.setDefaultDateRange();\n    // No automatic API call - user must click Search button\n  }\n\n  searchDashboard() {\n    // This method is called when Search button is clicked\n    this.loadDashboardData();\n  }\n  resetFilters() {\n    // Reset all filters to default values\n    this.selectedLocations = this.branches.map(branch => branch.restaurantIdOld);\n    this.setDefaultDateRange();\n    this.baseDateCtrl.setValue('deliveryDate');\n    this.searchQuery.setValue('');\n    this.locationFilterCtrl.setValue('');\n    this.filteredBranches = [...this.branches];\n    // Load dashboard data with reset filters\n    this.loadDashboardData();\n  }\n  onSearchQuery() {\n    const query = this.searchQuery.value?.trim();\n    if (query) {\n      this.loadDashboardDataWithQuery(query);\n    } else {\n      this.loadDashboardData();\n    }\n  }\n  loadDashboardDataWithQuery(query) {\n    if (!this.startDate.value || !this.endDate.value || !this.selectedDashboard) {\n      return;\n    }\n    this.isLoading = true;\n    const filters = {\n      locations: this.selectedLocations || [],\n      startDate: this.formatDate(this.startDate.value),\n      endDate: this.formatDate(this.endDate.value),\n      baseDate: this.baseDateCtrl.value || 'deliveryDate'\n    };\n    const request = {\n      tenant_id: this.user.tenantId,\n      filters: filters,\n      user_query: query,\n      use_default_charts: false,\n      dashboard_type: this.selectedDashboard\n    };\n    this.smartDashboardService.getSmartDashboardData(request).pipe(takeUntil(this.destroy$)).subscribe({\n      next: response => {\n        if (response.status === 'success') {\n          this.processDashboardData(response.data);\n        } else {\n          this.clearDashboardData();\n        }\n        this.isLoading = false;\n        this.cdr.detectChanges();\n      },\n      error: () => {\n        this.clearDashboardData();\n        this.isLoading = false;\n        this.cdr.detectChanges();\n      }\n    });\n  }\n  // Dynamic chart methods using services\n  getChartData(chart) {\n    return chart.data;\n  }\n  getChartType(chart) {\n    return this.chartRenderer.getChartType(chart.type);\n  }\n  getChartOptions(chart) {\n    return chart.options || this.configService.getDefaultChartOptions();\n  }\n  getChartCssClass(chart) {\n    return this.chartRenderer.getChartCssClass(chart);\n  }\n  static {\n    this.ɵfac = function SmartDashboardComponent_Factory(t) {\n      return new (t || SmartDashboardComponent)(i0.ɵɵdirectiveInject(i1.SmartDashboardService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.ShareDataService), i0.ɵɵdirectiveInject(i4.DashboardConfigService), i0.ɵɵdirectiveInject(i5.ChartRendererService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SmartDashboardComponent,\n      selectors: [[\"app-smart-dashboard\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 82,\n      vars: 18,\n      consts: [[1, \"smart-dashboard-container\"], [1, \"main-layout\"], [1, \"left-sidebar\"], [1, \"dashboard-selection\"], [\"appearance\", \"outline\", 1, \"dashboard-dropdown\"], [3, \"value\", \"disabled\", \"valueChange\"], [\"disabled\", \"\", 4, \"ngIf\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"filters-section\"], [1, \"filters-title\"], [1, \"filter-count\"], [1, \"filter-group\"], [1, \"filter-label\"], [\"appearance\", \"outline\", 1, \"filter-field\"], [\"multiple\", \"\", 3, \"value\", \"valueChange\"], [\"placeholderLabel\", \"Search locations...\", \"noEntriesFoundLabel\", \"No locations found\", 3, \"formControl\"], [\"class\", \"filter-group\", 4, \"ngIf\"], [\"matInput\", \"\", 3, \"matDatepicker\", \"formControl\"], [\"matSuffix\", \"\", 3, \"for\"], [\"startPicker\", \"\"], [\"endPicker\", \"\"], [1, \"filter-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"search-btn\", 3, \"click\"], [\"mat-stroked-button\", \"\", 1, \"reset-filters-btn\", 3, \"click\"], [1, \"right-content\"], [1, \"search-header\"], [1, \"assistant-info\"], [1, \"assistant-icon\"], [1, \"assistant-text\"], [1, \"assistant-title\"], [1, \"assistant-status\"], [1, \"search-container\"], [\"appearance\", \"outline\", 1, \"search-field\"], [\"matInput\", \"\", \"placeholder\", \"Ask me about your business data\", 3, \"formControl\", \"keyup.enter\"], [\"matSuffix\", \"\", 1, \"search-icon\", 3, \"click\"], [1, \"dashboard-content-area\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"dashboard-grid\", 4, \"ngIf\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [\"disabled\", \"\"], [3, \"value\"], [3, \"formControl\", \"disabled\"], [1, \"loading-container\"], [\"diameter\", \"50\"], [1, \"dashboard-grid\"], [\"class\", \"summary-cards-row\", 4, \"ngIf\"], [\"class\", \"charts-grid\", 4, \"ngIf\"], [1, \"summary-cards-row\"], [\"class\", \"summary-card\", 3, \"border-left-color\", 4, \"ngFor\", \"ngForOf\"], [1, \"summary-card\"], [1, \"card-content\"], [1, \"card-icon\"], [1, \"card-info\"], [1, \"card-value\"], [1, \"card-label\"], [1, \"charts-grid\"], [\"class\", \"chart-card\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [1, \"chart-card\", 3, \"ngClass\"], [1, \"chart-title\"], [1, \"chart-container\"], [\"baseChart\", \"\", 3, \"type\", \"data\", \"options\"], [1, \"empty-state\"], [1, \"empty-icon\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"]],\n      template: function SmartDashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"mat-form-field\", 4)(5, \"mat-label\");\n          i0.ɵɵtext(6, \"Select Dashboard\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"mat-select\", 5);\n          i0.ɵɵlistener(\"valueChange\", function SmartDashboardComponent_Template_mat_select_valueChange_7_listener($event) {\n            return ctx.selectedDashboard = $event;\n          });\n          i0.ɵɵtemplate(8, SmartDashboardComponent_mat_option_8_Template, 2, 0, \"mat-option\", 6);\n          i0.ɵɵtemplate(9, SmartDashboardComponent_mat_option_9_Template, 2, 2, \"mat-option\", 7);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(10, \"div\", 8)(11, \"h3\", 9)(12, \"mat-icon\");\n          i0.ɵɵtext(13, \"tune\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(14, \" Smart Filters \");\n          i0.ɵɵelementStart(15, \"span\", 10);\n          i0.ɵɵtext(16, \"1\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(17, \"div\", 11)(18, \"h4\", 12)(19, \"mat-icon\");\n          i0.ɵɵtext(20, \"restaurant\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(21, \" Restaurants \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"mat-form-field\", 13)(23, \"mat-label\");\n          i0.ɵɵtext(24, \"Select restaurants\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"mat-select\", 14);\n          i0.ɵɵlistener(\"valueChange\", function SmartDashboardComponent_Template_mat_select_valueChange_25_listener($event) {\n            return ctx.selectedLocations = $event;\n          });\n          i0.ɵɵelementStart(26, \"mat-option\");\n          i0.ɵɵelement(27, \"ngx-mat-select-search\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(28, SmartDashboardComponent_mat_option_28_Template, 2, 2, \"mat-option\", 7);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(29, SmartDashboardComponent_div_29_Template, 11, 4, \"div\", 16);\n          i0.ɵɵelementStart(30, \"div\", 11)(31, \"h4\", 12)(32, \"mat-icon\");\n          i0.ɵɵtext(33, \"date_range\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(34, \" Start Date \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"mat-form-field\", 13)(36, \"mat-label\");\n          i0.ɵɵtext(37, \"Start Date\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(38, \"input\", 17)(39, \"mat-datepicker-toggle\", 18)(40, \"mat-datepicker\", null, 19);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(42, \"div\", 11)(43, \"h4\", 12)(44, \"mat-icon\");\n          i0.ɵɵtext(45, \"date_range\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(46, \" End Date \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"mat-form-field\", 13)(48, \"mat-label\");\n          i0.ɵɵtext(49, \"End Date\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(50, \"input\", 17)(51, \"mat-datepicker-toggle\", 18)(52, \"mat-datepicker\", null, 20);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(54, \"div\", 21)(55, \"button\", 22);\n          i0.ɵɵlistener(\"click\", function SmartDashboardComponent_Template_button_click_55_listener() {\n            return ctx.searchDashboard();\n          });\n          i0.ɵɵelementStart(56, \"mat-icon\");\n          i0.ɵɵtext(57, \"search\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(58, \" Search \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(59, \"button\", 23);\n          i0.ɵɵlistener(\"click\", function SmartDashboardComponent_Template_button_click_59_listener() {\n            return ctx.resetFilters();\n          });\n          i0.ɵɵelementStart(60, \"mat-icon\");\n          i0.ɵɵtext(61, \"refresh\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(62, \" Reset \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(63, \"div\", 24)(64, \"div\", 25)(65, \"div\", 26)(66, \"mat-icon\", 27);\n          i0.ɵɵtext(67, \"smart_toy\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(68, \"div\", 28)(69, \"span\", 29);\n          i0.ɵɵtext(70, \"Smart Dashboard Assistant\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(71, \"span\", 30);\n          i0.ɵɵtext(72, \"Ready to analyze\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(73, \"div\", 31)(74, \"mat-form-field\", 32)(75, \"input\", 33);\n          i0.ɵɵlistener(\"keyup.enter\", function SmartDashboardComponent_Template_input_keyup_enter_75_listener() {\n            return ctx.onSearchQuery();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(76, \"mat-icon\", 34);\n          i0.ɵɵlistener(\"click\", function SmartDashboardComponent_Template_mat_icon_click_76_listener() {\n            return ctx.onSearchQuery();\n          });\n          i0.ɵɵtext(77, \"search\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(78, \"div\", 35);\n          i0.ɵɵtemplate(79, SmartDashboardComponent_div_79_Template, 4, 0, \"div\", 36);\n          i0.ɵɵtemplate(80, SmartDashboardComponent_div_80_Template, 3, 2, \"div\", 37);\n          i0.ɵɵtemplate(81, SmartDashboardComponent_div_81_Template, 11, 0, \"div\", 38);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          const _r4 = i0.ɵɵreference(41);\n          const _r5 = i0.ɵɵreference(53);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"value\", ctx.selectedDashboard)(\"disabled\", !ctx.isConfigLoaded);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isConfigLoaded);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.dashboardTypes);\n          i0.ɵɵadvance(16);\n          i0.ɵɵproperty(\"value\", ctx.selectedLocations);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formControl\", ctx.locationFilterCtrl);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.filteredBranches);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedDashboard === \"purchase\");\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"matDatepicker\", _r4)(\"formControl\", ctx.startDate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"for\", _r4);\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"matDatepicker\", _r5)(\"formControl\", ctx.endDate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"for\", _r5);\n          i0.ɵɵadvance(24);\n          i0.ɵɵproperty(\"formControl\", ctx.searchQuery);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && (ctx.summaryCards.length > 0 || ctx.charts.length > 0));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.summaryCards.length === 0 && ctx.charts.length === 0);\n        }\n      },\n      dependencies: [CommonModule, i6.NgClass, i6.NgForOf, i6.NgIf, MatCardModule, i7.MatCard, i7.MatCardContent, i7.MatCardHeader, i7.MatCardTitle, MatButtonModule, i8.MatButton, MatIconModule, i9.MatIcon, MatSelectModule, i10.MatFormField, i10.MatLabel, i10.MatSuffix, i11.MatSelect, i12.MatOption, MatFormFieldModule, MatInputModule, i13.MatInput, MatDatepickerModule, i14.MatDatepicker, i14.MatDatepickerInput, i14.MatDatepickerToggle, MatNativeDateModule, MatDividerModule, MatProgressSpinnerModule, i15.MatProgressSpinner, NgChartsModule, i16.BaseChartDirective, NgxMatSelectSearchModule, i17.MatSelectSearchComponent, ReactiveFormsModule, i18.DefaultValueAccessor, i18.NgControlStatus, i18.FormControlDirective, FormsModule],\n      styles: [\".smart-dashboard-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  background-color: #f8f9fa;\\n  height: 100vh;\\n  overflow: hidden;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]     .mat-mdc-form-field .mat-mdc-text-field-wrapper {\\n  height: 36px !important;\\n  min-height: 36px !important;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]     .mat-mdc-form-field .mat-mdc-text-field-wrapper .mat-mdc-form-field-infix {\\n  padding: 6px 12px !important;\\n  min-height: 24px !important;\\n  border-top: none !important;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]     .mat-mdc-form-field .mat-mdc-text-field-wrapper .mat-mdc-form-field-flex {\\n  align-items: center !important;\\n  height: 36px !important;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]     .mat-mdc-form-field .mat-mdc-form-field-subscript-wrapper {\\n  display: none !important;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]     .mat-mdc-form-field .mat-mdc-form-field-outline {\\n  color: #dee2e6 !important;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]     .mat-mdc-form-field .mat-mdc-form-field-outline-thick {\\n  color: #ffb366 !important;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]     .mat-mdc-form-field .mat-mdc-form-field-label {\\n  color: #666 !important;\\n  font-size: 13px !important;\\n  top: 18px !important;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]     .mat-mdc-form-field.mat-focused .mat-mdc-form-field-label {\\n  color: #ffb366 !important;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]     .mat-mdc-form-field.mat-form-field-should-float .mat-mdc-form-field-label {\\n  transform: translateY(-12px) scale(0.75) !important;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]     .mat-mdc-select .mat-mdc-select-trigger {\\n  height: 36px !important;\\n  display: flex !important;\\n  align-items: center !important;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]     .mat-mdc-select .mat-mdc-select-value {\\n  font-size: 13px !important;\\n  line-height: 24px !important;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]     .mat-mdc-select .mat-mdc-select-arrow {\\n  color: #ffb366 !important;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]     .mat-mdc-select-panel .mat-mdc-option {\\n  height: 32px !important;\\n  line-height: 32px !important;\\n  font-size: 13px !important;\\n  padding: 0 16px !important;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]     .mat-mdc-select-panel .mat-mdc-option.mat-mdc-option-active {\\n  background: rgba(255, 179, 102, 0.1) !important;\\n  color: #ffb366 !important;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]     .mat-mdc-select-panel .mat-mdc-option:hover {\\n  background: rgba(255, 179, 102, 0.05) !important;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]     .mat-mdc-select-panel .mat-mdc-option.select-all-option {\\n  border-bottom: 1px solid #e9ecef !important;\\n  margin-bottom: 4px !important;\\n  background: #f8f9fa !important;\\n  font-weight: 500 !important;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]     .mat-mdc-select-panel .mat-mdc-option.select-all-option:hover {\\n  background: rgba(255, 179, 102, 0.08) !important;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]     .mat-mdc-select-panel .mat-mdc-option.select-all-option .mat-mdc-checkbox .mdc-checkbox__native-control:enabled:checked ~ .mdc-checkbox__background, .smart-dashboard-container[_ngcontent-%COMP%]     .mat-mdc-select-panel .mat-mdc-option.select-all-option .mat-mdc-checkbox .mdc-checkbox__native-control:enabled:indeterminate ~ .mdc-checkbox__background {\\n  background-color: #ffb366 !important;\\n  border-color: #ffb366 !important;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]     .mat-mdc-input-element {\\n  font-size: 13px !important;\\n  height: 24px !important;\\n  line-height: 24px !important;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]     .mat-datepicker-toggle .mat-icon {\\n  color: #ffb366 !important;\\n  font-size: 18px !important;\\n  width: 18px !important;\\n  height: 18px !important;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]     .mat-datepicker-content .mat-calendar-header {\\n  background: #ffb366 !important;\\n  color: white !important;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]     .mat-datepicker-content .mat-calendar-body-selected {\\n  background-color: #ffb366 !important;\\n  color: white !important;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]     .mat-datepicker-content .mat-calendar-body-today:not(.mat-calendar-body-selected) {\\n  border-color: #ffb366 !important;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]     .mat-mdc-raised-button, .smart-dashboard-container[_ngcontent-%COMP%]     .mat-mdc-outlined-button {\\n  height: 32px !important;\\n  line-height: 32px !important;\\n  padding: 0 12px !important;\\n  font-size: 13px !important;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]     .mat-mdc-raised-button.mat-primary, .smart-dashboard-container[_ngcontent-%COMP%]     .mat-mdc-outlined-button.mat-primary {\\n  background-color: #ffb366 !important;\\n  color: white !important;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]     .mat-mdc-raised-button.mat-primary:hover, .smart-dashboard-container[_ngcontent-%COMP%]     .mat-mdc-outlined-button.mat-primary:hover {\\n  background-color: #ffa64d !important;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]     .mat-mdc-outlined-button.mat-primary {\\n  border-color: #ffb366 !important;\\n  color: #ffb366 !important;\\n  background-color: transparent !important;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]     .mat-mdc-outlined-button.mat-primary:hover {\\n  background-color: rgba(255, 179, 102, 0.05) !important;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%] {\\n  display: flex;\\n  height: calc(100vh - 40px);\\n  overflow: hidden;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%] {\\n  width: 240px;\\n  background: linear-gradient(135deg, #ffffff 0%, #fff5f0 100%);\\n  border-right: 1px solid #ffe0cc;\\n  padding: 16px;\\n  box-shadow: 2px 0 4px rgba(255, 179, 102, 0.08);\\n  height: 100%;\\n  overflow-y: auto;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #fff2e6;\\n  border-radius: 3px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #ffb366;\\n  border-radius: 3px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #ffa64d;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]   .dashboard-selection[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n  padding: 0;\\n  background: none;\\n  border-radius: 0;\\n  position: relative;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]   .dashboard-selection[_ngcontent-%COMP%]   .dashboard-dropdown[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]   .dashboard-selection[_ngcontent-%COMP%]   .dashboard-dropdown[_ngcontent-%COMP%]     .mat-mdc-form-field-subscript-wrapper {\\n  display: none;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]   .dashboard-selection[_ngcontent-%COMP%]   .dashboard-dropdown[_ngcontent-%COMP%]     .mat-mdc-text-field-wrapper {\\n  background: linear-gradient(135deg, #fff8f5 0%, #ffffff 100%);\\n  border-radius: 6px;\\n  border: 1px solid #e9ecef;\\n  transition: all 0.2s ease;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]   .dashboard-selection[_ngcontent-%COMP%]   .dashboard-dropdown[_ngcontent-%COMP%]     .mat-mdc-text-field-wrapper:hover {\\n  border-color: #ffb366;\\n  box-shadow: 0 2px 6px rgba(255, 179, 102, 0.1);\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]   .dashboard-selection[_ngcontent-%COMP%]   .dashboard-dropdown[_ngcontent-%COMP%]     .mat-mdc-form-field-outline {\\n  display: none;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]   .dashboard-selection[_ngcontent-%COMP%]   .dashboard-dropdown[_ngcontent-%COMP%]     .mat-mdc-form-field-outline-thick {\\n  display: none;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]   .dashboard-selection[_ngcontent-%COMP%]   .dashboard-dropdown[_ngcontent-%COMP%]     .mat-mdc-form-field-label {\\n  color: #666;\\n  font-weight: 500;\\n  font-size: 12px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]   .dashboard-selection[_ngcontent-%COMP%]   .dashboard-dropdown[_ngcontent-%COMP%]     .mat-mdc-select-value {\\n  color: #333;\\n  font-weight: 600;\\n  font-size: 14px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]   .dashboard-selection[_ngcontent-%COMP%]   .dashboard-dropdown[_ngcontent-%COMP%]     .mat-mdc-select-arrow {\\n  color: #ffb366;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]   .dashboard-selection[_ngcontent-%COMP%]   .dashboard-dropdown[_ngcontent-%COMP%]     .mat-mdc-form-field-infix {\\n  padding: 8px 12px;\\n  min-height: 36px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]   .dashboard-selection[_ngcontent-%COMP%]   .dashboard-dropdown[_ngcontent-%COMP%]     .mat-mdc-select-trigger {\\n  height: 36px;\\n  display: flex;\\n  align-items: center;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]   .dashboard-selection[_ngcontent-%COMP%]   .dashboard-dropdown[_ngcontent-%COMP%]     .mat-mdc-form-field-flex {\\n  align-items: center;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]   .dashboard-selection[_ngcontent-%COMP%]   .dashboard-dropdown[_ngcontent-%COMP%]     .mat-focused .mat-mdc-text-field-wrapper {\\n  border-color: #ffb366 !important;\\n  box-shadow: 0 3px 8px rgba(255, 179, 102, 0.15) !important;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin: 0 0 16px 0;\\n  font-size: 15px;\\n  font-weight: 600;\\n  color: #333;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-title[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #ffb366;\\n  font-size: 18px;\\n  width: 18px;\\n  height: 18px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-title[_ngcontent-%COMP%]   .filter-count[_ngcontent-%COMP%] {\\n  background: #ffb366;\\n  color: white;\\n  border-radius: 50%;\\n  width: 18px;\\n  height: 18px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 10px;\\n  font-weight: bold;\\n  margin-left: auto;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%] {\\n  margin-bottom: 10px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-label[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  margin: 0 0 8px 0;\\n  font-size: 13px;\\n  font-weight: 500;\\n  color: #333;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-label[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  width: 14px;\\n  height: 14px;\\n  color: #ffb366;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n  margin-bottom: 6px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-field[_ngcontent-%COMP%]     .mat-mdc-form-field-subscript-wrapper {\\n  display: none;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-field[_ngcontent-%COMP%]     .mat-mdc-text-field-wrapper {\\n  background-color: white;\\n  border-radius: 4px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-field[_ngcontent-%COMP%]     .mat-mdc-form-field-outline {\\n  color: #dee2e6;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-field[_ngcontent-%COMP%]     .mat-mdc-form-field-outline-thick {\\n  color: #ffb366;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-field[_ngcontent-%COMP%]     .mat-mdc-form-field-label {\\n  font-size: 13px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filter-actions[_ngcontent-%COMP%] {\\n  margin-top: 20px;\\n  padding-top: 16px;\\n  border-top: 1px solid #e9ecef;\\n  display: flex;\\n  gap: 8px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filter-actions[_ngcontent-%COMP%]   .search-btn[_ngcontent-%COMP%] {\\n  flex: 1;\\n  background-color: #ffb366 !important;\\n  color: white !important;\\n  font-weight: 500;\\n  padding: 8px;\\n  border-radius: 4px;\\n  font-size: 13px;\\n  border: none;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filter-actions[_ngcontent-%COMP%]   .search-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #ffa64d !important;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filter-actions[_ngcontent-%COMP%]   .search-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  width: 16px;\\n  height: 16px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filter-actions[_ngcontent-%COMP%]   .reset-filters-btn[_ngcontent-%COMP%] {\\n  flex: 1;\\n  color: #6c757d;\\n  border-color: #dee2e6;\\n  font-weight: 500;\\n  padding: 8px;\\n  border-radius: 4px;\\n  font-size: 13px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filter-actions[_ngcontent-%COMP%]   .reset-filters-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9fa;\\n  border-color: #adb5bd;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filter-actions[_ngcontent-%COMP%]   .reset-filters-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  width: 16px;\\n  height: 16px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  background-color: #f8f9fa;\\n  height: 100%;\\n  overflow: hidden;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .search-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #fff8f5 0%, #ffffff 100%);\\n  border-bottom: 1px solid #e9ecef;\\n  padding: 10px 20px;\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n  min-height: 50px;\\n  box-shadow: 0 1px 3px rgba(255, 179, 102, 0.05);\\n  overflow: hidden;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .search-header[_ngcontent-%COMP%]   .assistant-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  flex-shrink: 0;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .search-header[_ngcontent-%COMP%]   .assistant-info[_ngcontent-%COMP%]   .assistant-icon[_ngcontent-%COMP%] {\\n  color: #ffb366;\\n  font-size: 18px;\\n  width: 18px;\\n  height: 18px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .search-header[_ngcontent-%COMP%]   .assistant-info[_ngcontent-%COMP%]   .assistant-text[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .search-header[_ngcontent-%COMP%]   .assistant-info[_ngcontent-%COMP%]   .assistant-text[_ngcontent-%COMP%]   .assistant-title[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: #333;\\n  white-space: nowrap;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .search-header[_ngcontent-%COMP%]   .assistant-info[_ngcontent-%COMP%]   .assistant-text[_ngcontent-%COMP%]   .assistant-status[_ngcontent-%COMP%] {\\n  font-size: 10px;\\n  color: #28a745;\\n  font-weight: 500;\\n  background: #e8f5e8;\\n  padding: 2px 8px;\\n  border-radius: 12px;\\n  display: inline-block;\\n  white-space: nowrap;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .search-header[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 0;\\n  max-width: calc(100% - 200px);\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .search-header[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n  max-width: 100%;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .search-header[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%]     .mat-mdc-form-field-subscript-wrapper {\\n  display: none;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .search-header[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%]     .mat-mdc-text-field-wrapper {\\n  background-color: white;\\n  border-radius: 24px;\\n  height: 36px;\\n  border: 1px solid #e9ecef;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);\\n  transition: all 0.3s ease;\\n  width: 100%;\\n  max-width: 100%;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .search-header[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%]     .mat-mdc-text-field-wrapper:hover {\\n  border-color: #ffb366;\\n  box-shadow: 0 4px 12px rgba(255, 179, 102, 0.1);\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .search-header[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%]     .mat-mdc-form-field-outline {\\n  display: none;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .search-header[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%]     .mat-mdc-form-field-outline-thick {\\n  display: none;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .search-header[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%]     .mat-mdc-form-field-infix {\\n  padding: 6px 16px;\\n  border-top: none;\\n  width: 100%;\\n  max-width: 100%;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .search-header[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%]     .mat-mdc-form-field-flex {\\n  align-items: center;\\n  height: 36px;\\n  width: 100%;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .search-header[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%]     input {\\n  font-size: 14px;\\n  color: #333;\\n  font-weight: 400;\\n  width: 100%;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .search-header[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%]     input::placeholder {\\n  color: #999;\\n  font-size: 14px;\\n  font-weight: 400;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .search-header[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%]     .mat-focused .mat-mdc-text-field-wrapper {\\n  border-color: #ffb366 !important;\\n  box-shadow: 0 4px 16px rgba(255, 179, 102, 0.15) !important;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .search-header[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%]   .search-icon[_ngcontent-%COMP%] {\\n  color: #999;\\n  cursor: pointer;\\n  font-size: 16px;\\n  transition: color 0.2s ease;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .search-header[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%]   .search-icon[_ngcontent-%COMP%]:hover {\\n  color: #ffb366;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  flex: 1;\\n  overflow-y: auto;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n  border-radius: 3px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #ffb366;\\n  border-radius: 3px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #ffa64d;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  height: 400px;\\n  gap: 16px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  font-size: 16px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .summary-cards-row[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(4, 1fr);\\n  gap: 16px;\\n  margin-bottom: 20px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .summary-cards-row[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 8px;\\n  border: 1px solid #e9ecef;\\n  transition: box-shadow 0.2s ease;\\n  position: relative;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .summary-cards-row[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .summary-cards-row[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]     .mat-mdc-card-content {\\n  padding: 16px !important;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .summary-cards-row[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .summary-cards-row[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 8px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background: linear-gradient(135deg, #ffb366 0%, #ffc999 100%);\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .summary-cards-row[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  width: 20px;\\n  height: 20px;\\n  color: white;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .summary-cards-row[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .card-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .summary-cards-row[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .card-info[_ngcontent-%COMP%]   .card-value[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: #333;\\n  margin-bottom: 4px;\\n  line-height: 1.2;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .summary-cards-row[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .card-info[_ngcontent-%COMP%]   .card-label[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #6c757d;\\n  font-weight: 500;\\n  line-height: 1.3;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(12, 1fr);\\n  gap: 16px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 12px;\\n  border: 1px solid #e9ecef;\\n  transition: box-shadow 0.2s ease;\\n  height: 400px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 4px 12px rgba(255, 179, 102, 0.1);\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card.full-width[_ngcontent-%COMP%] {\\n  grid-column: span 12;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card.half-width[_ngcontent-%COMP%] {\\n  grid-column: span 6;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card.third-width[_ngcontent-%COMP%] {\\n  grid-column: span 4;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]   .chart-title[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: #333;\\n  margin: 0;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  height: 320px;\\n  padding: 16px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%]   canvas[_ngcontent-%COMP%] {\\n  width: 100% !important;\\n  height: 100% !important;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%]   .no-data-message[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  height: 280px;\\n  color: #adb5bd;\\n  text-align: center;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%]   .no-data-message[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 40px;\\n  width: 40px;\\n  height: 40px;\\n  margin-bottom: 8px;\\n  color: #ffb366;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%]   .no-data-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 13px;\\n  font-weight: 500;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  height: 400px;\\n  text-align: center;\\n  color: #666;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%] {\\n  font-size: 64px;\\n  width: 64px;\\n  height: 64px;\\n  margin-bottom: 16px;\\n  opacity: 0.5;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  font-size: 20px;\\n}\\n.smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 24px 0;\\n  font-size: 14px;\\n}\\n\\n@media (max-width: 1400px) {\\n  .smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .summary-cards-row[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(2, 1fr);\\n  }\\n  .smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card.half-width[_ngcontent-%COMP%], .smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card.third-width[_ngcontent-%COMP%] {\\n    grid-column: span 12;\\n  }\\n}\\n@media (max-width: 1024px) {\\n  .smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .search-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 16px;\\n    align-items: stretch;\\n  }\\n  .smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .search-header[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n  .smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%] {\\n    width: 220px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  .smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .left-sidebar[_ngcontent-%COMP%] {\\n    width: 100%;\\n    border-right: none;\\n    border-bottom: 1px solid #e9ecef;\\n    padding: 12px;\\n  }\\n  .smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .search-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 12px;\\n    padding: 12px 16px;\\n  }\\n  .smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .search-header[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n  .smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n  .smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .summary-cards-row[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 12px;\\n  }\\n  .smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%] {\\n    gap: 12px;\\n  }\\n  .smart-dashboard-container[_ngcontent-%COMP%]   .main-layout[_ngcontent-%COMP%]   .right-content[_ngcontent-%COMP%]   .dashboard-content-area[_ngcontent-%COMP%]   .dashboard-grid[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%] {\\n    grid-column: span 12 !important;\\n  }\\n}\\n  .chart-container canvas {\\n  max-width: 100% !important;\\n  height: auto !important;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}\nexport { SmartDashboardComponent };", "map": {"version": 3, "names": ["CommonModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatSelectModule", "MatFormFieldModule", "MatInputModule", "MatDatepickerModule", "MatNativeDateModule", "MatDividerModule", "MatProgressSpinnerModule", "NgChartsModule", "NgxMatSelectSearchModule", "FormControl", "ReactiveFormsModule", "FormsModule", "Subject", "takeUntil", "debounceTime", "distinctUntilChanged", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "dashboardType_r9", "value", "ɵɵadvance", "ɵɵtextInterpolate1", "label", "branch_r10", "restaurantIdOld", "branchName", "baseDateOption_r13", "ɵɵtemplate", "SmartDashboardComponent_div_29_mat_option_9_Template", "SmartDashboardComponent_div_29_mat_option_10_Template", "ctx_r3", "baseDateCtrl", "isConfigLoaded", "baseDateOptions", "ɵɵelement", "ɵɵstyleProp", "card_r17", "color", "ɵɵtextInterpolate", "icon", "SmartDashboardComponent_div_80_div_1_mat_card_1_Template", "ctx_r14", "summaryCards", "ctx_r18", "getChartCssClass", "chart_r19", "title", "ɵɵattribute", "type", "getChartType", "getChartData", "getChartOptions", "SmartDashboardComponent_div_80_div_2_mat_card_1_Template", "ctx_r15", "charts", "SmartDashboardComponent_div_80_div_1_Template", "SmartDashboardComponent_div_80_div_2_Template", "ctx_r7", "length", "ɵɵlistener", "SmartDashboardComponent_div_81_Template_button_click_7_listener", "ɵɵrestoreView", "_r22", "ctx_r21", "ɵɵnextContext", "ɵɵresetView", "searchDashboard", "SmartDashboardComponent", "constructor", "smartDashboardService", "authService", "shareDataService", "configService", "<PERSON><PERSON><PERSON><PERSON>", "cdr", "destroy$", "branches", "filteredBranches", "selectedLocations", "locationFilterCtrl", "startDate", "endDate", "searchQuery", "selectedDashboard", "isLoading", "dashboardTypes", "user", "getCurrentUser", "initializeConfig", "loadConfig", "subscribe", "next", "response", "status", "setConfig", "data", "setupDynamicConfigurations", "setupDefaultConfigurations", "detectChanges", "error", "config", "dashboard_types", "base_date_options", "uiConfig", "ui_config", "default_dashboard_type", "setValue", "default_base_date", "setDefaultDateRange", "ngOnInit", "initializeFilters", "loadBranches", "ngOnDestroy", "complete", "valueChanges", "pipe", "filterBranches", "selectedBranchesSource", "map", "branch", "searchTerm", "normalizedSearchTerm", "toLowerCase", "replace", "filter", "includes", "loadDashboardData", "filters", "locations", "formatDate", "baseDate", "request", "tenant_id", "tenantId", "user_query", "use_default_charts", "dashboard_type", "getSmartDashboardData", "processDashboardData", "clearDashboardData", "summary_items", "item", "getSummaryCardIcon", "data_type", "getSummaryCardColor", "chart", "processChart", "date", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "today", "Date", "startOfMonth", "thirtyDaysAgo", "getTime", "onLocationChange", "onDateChange", "onDashboardChange", "resetFilters", "onSearchQuery", "query", "trim", "loadDashboardDataWithQuery", "options", "getDefaultChartOptions", "ɵɵdirectiveInject", "i1", "SmartDashboardService", "i2", "AuthService", "i3", "ShareDataService", "i4", "DashboardConfigService", "i5", "ChartRendererService", "ChangeDetectorRef", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "SmartDashboardComponent_Template", "rf", "ctx", "SmartDashboardComponent_Template_mat_select_valueChange_7_listener", "$event", "SmartDashboardComponent_mat_option_8_Template", "SmartDashboardComponent_mat_option_9_Template", "SmartDashboardComponent_Template_mat_select_valueChange_25_listener", "SmartDashboardComponent_mat_option_28_Template", "SmartDashboardComponent_div_29_Template", "SmartDashboardComponent_Template_button_click_55_listener", "SmartDashboardComponent_Template_button_click_59_listener", "SmartDashboardComponent_Template_input_keyup_enter_75_listener", "SmartDashboardComponent_Template_mat_icon_click_76_listener", "SmartDashboardComponent_div_79_Template", "SmartDashboardComponent_div_80_Template", "SmartDashboardComponent_div_81_Template", "_r4", "_r5", "i6", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i7", "MatCard", "Mat<PERSON><PERSON><PERSON><PERSON>nt", "MatCardHeader", "MatCardTitle", "i8", "MatButton", "i9", "MatIcon", "i10", "MatFormField", "<PERSON><PERSON><PERSON><PERSON>", "MatSuffix", "i11", "MatSelect", "i12", "MatOption", "i13", "MatInput", "i14", "MatDatepicker", "MatDatepickerInput", "MatDatepickerToggle", "i15", "MatProgressSpinner", "i16", "BaseChartDirective", "i17", "MatSelectSearchComponent", "i18", "DefaultValueAccessor", "NgControlStatus", "FormControlDirective", "styles"], "sources": ["C:\\Users\\<USER>\\Desktop\\digii\\digitorywebv4\\src\\app\\pages\\smart-dashboard\\smart-dashboard.component.ts", "C:\\Users\\<USER>\\Desktop\\digii\\digitorywebv4\\src\\app\\pages\\smart-dashboard\\smart-dashboard.component.html"], "sourcesContent": ["import { <PERSON>mpo<PERSON>, On<PERSON>ni<PERSON>, <PERSON><PERSON><PERSON>roy, ChangeDetectorRef } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { NgChartsModule } from 'ng2-charts';\nimport { NgxMatSelectSearchModule } from 'ngx-mat-select-search';\nimport { FormControl, ReactiveFormsModule, FormsModule } from '@angular/forms';\nimport { Subject, takeUntil, debounceTime, distinctUntilChanged } from 'rxjs';\nimport { ChartConfiguration, ChartData, ChartType } from 'chart.js';\n\nimport { SmartDashboardService } from '../../services/smart-dashboard.service';\nimport { AuthService } from '../../services/auth.service';\nimport { ShareDataService } from '../../services/share-data.service';\nimport { DashboardConfigService, DashboardType, BaseDateOption } from '../../services/dashboard-config.service';\nimport { ChartRendererService, ChartModel } from '../../services/chart-renderer.service';\n\ninterface SummaryCard {\n  icon: string;\n  value: string;\n  label: string;\n  color: string;\n  data_type?: string;\n}\n\n@Component({\n  selector: 'app-smart-dashboard',\n  standalone: true,\n  imports: [\n    CommonModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatSelectModule,\n    MatFormFieldModule,\n    MatInputModule,\n    MatDatepickerModule,\n    MatNativeDateModule,\n    MatDividerModule,\n    MatProgressSpinnerModule,\n    NgChartsModule,\n    NgxMatSelectSearchModule,\n    ReactiveFormsModule,\n    FormsModule\n  ],\n  templateUrl: './smart-dashboard.component.html',\n  styleUrls: ['./smart-dashboard.component.scss']\n})\nexport class SmartDashboardComponent implements OnInit, OnDestroy {\n  private destroy$ = new Subject<void>();\n  \n  // User and branch data\n  user: any;\n  branches: any[] = [];\n  filteredBranches: any[] = [];\n  selectedLocations: string[] = [];\n  \n  // Form controls\n  locationFilterCtrl = new FormControl('');\n  startDate = new FormControl();\n  endDate = new FormControl();\n  searchQuery = new FormControl('');\n  baseDateCtrl = new FormControl();\n  selectedDashboard = '';\n\n  // Dashboard data\n  summaryCards: SummaryCard[] = [];\n  charts: ChartModel[] = [];\n  isLoading = false;\n  isConfigLoaded = false;\n\n  // Dynamic configuration data\n  dashboardTypes: DashboardType[] = [];\n  baseDateOptions: BaseDateOption[] = [];\n\n\n\n\n\n  constructor(\n    private smartDashboardService: SmartDashboardService,\n    private authService: AuthService,\n    private shareDataService: ShareDataService,\n    private configService: DashboardConfigService,\n    private chartRenderer: ChartRendererService,\n    private cdr: ChangeDetectorRef\n  ) {\n    this.user = this.authService.getCurrentUser();\n    this.initializeConfig();\n  }\n\n  private initializeConfig(): void {\n    // Load dashboard configuration on component initialization\n    this.configService.loadConfig().subscribe({\n      next: (response) => {\n        if (response.status === 'success') {\n          this.configService.setConfig(response.data);\n          this.setupDynamicConfigurations(response.data);\n        } else {\n          this.setupDefaultConfigurations();\n        }\n        this.isConfigLoaded = true;\n        this.cdr.detectChanges();\n      },\n      error: () => {\n        this.setupDefaultConfigurations();\n        this.isConfigLoaded = true;\n        this.cdr.detectChanges();\n      }\n    });\n  }\n\n  private setupDynamicConfigurations(config: any): void {\n    // Set dashboard types\n    this.dashboardTypes = config.dashboard_types || [];\n\n    // Set base date options\n    this.baseDateOptions = config.base_date_options || [];\n\n    // Set default values from UI config\n    const uiConfig = config.ui_config || {};\n\n    // Set default form values\n    this.selectedDashboard = uiConfig.default_dashboard_type || 'purchase';\n    this.baseDateCtrl.setValue(uiConfig.default_base_date || 'deliveryDate');\n\n    // Set date range based on dashboard type\n    this.setDefaultDateRange();\n\n    // Configuration is set - user can now click Search to load data\n    // No automatic data loading\n  }\n\n  private setupDefaultConfigurations(): void {\n    // Fallback configurations if backend fails\n    this.dashboardTypes = [\n      { value: 'purchase', label: 'Purchase Dashboard' },\n      { value: 'inventory', label: 'Inventory Dashboard' },\n    ];\n    this.baseDateOptions = [\n      { value: 'deliveryDate', label: 'Delivery Date' },\n      { value: 'orderDate', label: 'Order Date' },\n      { value: 'createdDate', label: 'Created Date' }\n    ];\n    this.selectedDashboard = 'inventory';\n    this.baseDateCtrl.setValue('deliveryDate');\n\n    // Set date range based on dashboard type\n    this.setDefaultDateRange();\n\n    // Fallback configuration is set - user can now click Search to load data\n    // No automatic data loading\n  }\n\n  ngOnInit(): void {\n    this.initializeFilters();\n    this.loadBranches();\n    // Don't load dashboard data immediately - wait for config to load\n    // Dashboard data will be loaded after config is ready\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  private initializeFilters(): void {\n    // Location filter\n    this.locationFilterCtrl.valueChanges\n      .pipe(\n        debounceTime(200),\n        distinctUntilChanged(),\n        takeUntil(this.destroy$)\n      )\n      .subscribe((value: string | null) => {\n        this.filterBranches(value || '');\n      });\n  }\n\n  private loadBranches(): void {\n    this.shareDataService.selectedBranchesSource\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(data => {\n        this.branches = data || [];\n        this.filteredBranches = [...this.branches];\n\n        // Select all branches by default\n        this.selectedLocations = this.branches.map(branch => branch.restaurantIdOld);\n      });\n  }\n\n  private filterBranches(searchTerm: string): void {\n    if (!searchTerm) {\n      this.filteredBranches = [...this.branches];\n    } else {\n      const normalizedSearchTerm = searchTerm.toLowerCase().replace(/\\s/g, '');\n      this.filteredBranches = this.branches.filter(branch =>\n        branch.branchName.toLowerCase().replace(/\\s/g, '').includes(normalizedSearchTerm)\n      );\n    }\n  }\n\n  loadDashboardData(): void {\n    if (!this.startDate.value || !this.endDate.value || !this.selectedDashboard) {\n      return;\n    }\n\n    this.isLoading = true;\n\n    const filters = {\n      locations: this.selectedLocations || [],\n      startDate: this.formatDate(this.startDate.value),\n      endDate: this.formatDate(this.endDate.value),\n      baseDate: this.baseDateCtrl.value || 'deliveryDate'\n    };\n\n    const request = {\n      tenant_id: this.user.tenantId,\n      filters: filters,\n      user_query: '',\n      use_default_charts: true,\n      dashboard_type: this.selectedDashboard\n    };\n\n    this.smartDashboardService.getSmartDashboardData(request)\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: (response) => {\n          if (response.status === 'success') {\n            this.processDashboardData(response.data);\n          } else {\n            this.clearDashboardData();\n          }\n          this.isLoading = false;\n          this.cdr.detectChanges();\n        },\n        error: () => {\n          this.clearDashboardData();\n          this.isLoading = false;\n          this.cdr.detectChanges();\n        }\n      });\n  }\n\n  private clearDashboardData(): void {\n    this.summaryCards = [];\n    this.charts = [];\n  }\n\n  private processDashboardData(data: any): void {\n    // Process summary cards using config service\n    this.summaryCards = data.summary_items?.map((item: any) => ({\n      icon: item.icon || this.configService.getSummaryCardIcon(item.data_type),\n      value: item.value,\n      label: item.label,\n      color: this.configService.getSummaryCardColor(item.data_type),\n      data_type: item.data_type\n    })) || [];\n\n    // Process charts using chart renderer service\n    this.charts = data.charts?.map((chart: any) =>\n      this.chartRenderer.processChart(chart)\n    ) || [];\n  }\n\n  private formatDate(date: Date): string {\n    // Fix the date offset issue by using local date components\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const day = String(date.getDate()).padStart(2, '0');\n    return `${year}-${month}-${day}`;\n  }\n\n  private setDefaultDateRange(): void {\n    const today = new Date();\n\n    if (this.selectedDashboard === 'inventory') {\n      // For inventory dashboard: current month start to current date\n      const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);\n      this.startDate.setValue(startOfMonth);\n      this.endDate.setValue(today);\n    } else {\n      // For purchase dashboard: last 30 days (existing behavior)\n      const thirtyDaysAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);\n      this.startDate.setValue(thirtyDaysAgo);\n      this.endDate.setValue(today);\n    }\n  }\n\n  onLocationChange(): void {\n    // No automatic API call - user must click Search button\n  }\n\n  onDateChange(): void {\n    // No automatic API call - user must click Search button\n  }\n\n  onDashboardChange(): void {\n    // Update date range when dashboard type changes\n    this.setDefaultDateRange();\n    // No automatic API call - user must click Search button\n  }\n\n  searchDashboard(): void {\n    // This method is called when Search button is clicked\n    this.loadDashboardData();\n  }\n\n  resetFilters(): void {\n    // Reset all filters to default values\n    this.selectedLocations = this.branches.map(branch => branch.restaurantIdOld);\n    this.setDefaultDateRange();\n    this.baseDateCtrl.setValue('deliveryDate');\n    this.searchQuery.setValue('');\n    this.locationFilterCtrl.setValue('');\n    this.filteredBranches = [...this.branches];\n\n    // Load dashboard data with reset filters\n    this.loadDashboardData();\n  }\n\n  onSearchQuery(): void {\n    const query = this.searchQuery.value?.trim();\n    if (query) {\n      this.loadDashboardDataWithQuery(query);\n    } else {\n      this.loadDashboardData();\n    }\n  }\n\n  private loadDashboardDataWithQuery(query: string): void {\n    if (!this.startDate.value || !this.endDate.value || !this.selectedDashboard) {\n      return;\n    }\n\n    this.isLoading = true;\n\n    const filters = {\n      locations: this.selectedLocations || [],\n      startDate: this.formatDate(this.startDate.value),\n      endDate: this.formatDate(this.endDate.value),\n      baseDate: this.baseDateCtrl.value || 'deliveryDate'\n    };\n\n    const request = {\n      tenant_id: this.user.tenantId,\n      filters: filters,\n      user_query: query,\n      use_default_charts: false,\n      dashboard_type: this.selectedDashboard\n    };\n\n    this.smartDashboardService.getSmartDashboardData(request)\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: (response) => {\n          if (response.status === 'success') {\n            this.processDashboardData(response.data);\n          } else {\n            this.clearDashboardData();\n          }\n          this.isLoading = false;\n          this.cdr.detectChanges();\n        },\n        error: () => {\n          this.clearDashboardData();\n          this.isLoading = false;\n          this.cdr.detectChanges();\n        }\n      });\n  }\n\n  // Dynamic chart methods using services\n  getChartData(chart: ChartModel): ChartData {\n    return chart.data;\n  }\n\n  getChartType(chart: ChartModel): ChartType {\n    return this.chartRenderer.getChartType(chart.type);\n  }\n\n  getChartOptions(chart: ChartModel): ChartConfiguration['options'] {\n    return chart.options || this.configService.getDefaultChartOptions();\n  }\n\n  getChartCssClass(chart: ChartModel): string {\n    return this.chartRenderer.getChartCssClass(chart);\n  }\n}\n", "<div class=\"smart-dashboard-container\">\n  <!-- Main Layout -->\n  <div class=\"main-layout\">\n    <!-- Left Sidebar -->\n    <div class=\"left-sidebar\">\n      <!-- Dashboard Selection -->\n      <div class=\"dashboard-selection\">\n        <mat-form-field appearance=\"outline\" class=\"dashboard-dropdown\">\n          <mat-label>Select Dashboard</mat-label>\n          <mat-select [(value)]=\"selectedDashboard\" [disabled]=\"!isConfigLoaded\">\n            <mat-option *ngIf=\"!isConfigLoaded\" disabled>Loading...</mat-option>\n            <mat-option *ngFor=\"let dashboardType of dashboardTypes\" [value]=\"dashboardType.value\">\n              {{dashboardType.label}}\n            </mat-option>\n          </mat-select>\n        </mat-form-field>\n      </div>\n      <!-- Smart Filters Section -->\n      <div class=\"filters-section\">\n        <h3 class=\"filters-title\">\n          <mat-icon>tune</mat-icon>\n          Smart Filters\n          <span class=\"filter-count\">1</span>\n        </h3>\n\n        <!-- Restaurants Filter -->\n        <div class=\"filter-group\">\n          <h4 class=\"filter-label\">\n            <mat-icon>restaurant</mat-icon>\n            Restaurants\n          </h4>\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <mat-label>Select restaurants</mat-label>\n            <mat-select [(value)]=\"selectedLocations\" multiple>\n              <mat-option>\n                <ngx-mat-select-search\n                  [formControl]=\"locationFilterCtrl\"\n                  placeholderLabel=\"Search locations...\"\n                  noEntriesFoundLabel=\"No locations found\">\n                </ngx-mat-select-search>\n              </mat-option>\n\n              <mat-option *ngFor=\"let branch of filteredBranches\" [value]=\"branch.restaurantIdOld\">\n                {{branch.branchName}}\n              </mat-option>\n            </mat-select>\n          </mat-form-field>\n        </div>\n\n        <!-- Base Date Filter - Only for Purchase Dashboard -->\n        <div class=\"filter-group\" *ngIf=\"selectedDashboard === 'purchase'\">\n          <h4 class=\"filter-label\">\n            <mat-icon>event</mat-icon>\n            Base Date\n          </h4>\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <mat-label>Select base date</mat-label>\n            <mat-select [formControl]=\"baseDateCtrl\" [disabled]=\"!isConfigLoaded\">\n              <mat-option *ngIf=\"!isConfigLoaded\" disabled>Loading...</mat-option>\n              <mat-option *ngFor=\"let baseDateOption of baseDateOptions\" [value]=\"baseDateOption.value\">\n                {{baseDateOption.label}}\n              </mat-option>\n            </mat-select>\n          </mat-form-field>\n        </div>\n\n        <!-- Start Date Filter -->\n        <div class=\"filter-group\">\n          <h4 class=\"filter-label\">\n            <mat-icon>date_range</mat-icon>\n            Start Date\n          </h4>\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <mat-label>Start Date</mat-label>\n            <input matInput [matDatepicker]=\"startPicker\" [formControl]=\"startDate\">\n            <mat-datepicker-toggle matSuffix [for]=\"startPicker\"></mat-datepicker-toggle>\n            <mat-datepicker #startPicker></mat-datepicker>\n          </mat-form-field>\n        </div>\n\n        <!-- End Date Filter -->\n        <div class=\"filter-group\">\n          <h4 class=\"filter-label\">\n            <mat-icon>date_range</mat-icon>\n            End Date\n          </h4>\n          <mat-form-field appearance=\"outline\" class=\"filter-field\">\n            <mat-label>End Date</mat-label>\n            <input matInput [matDatepicker]=\"endPicker\" [formControl]=\"endDate\">\n            <mat-datepicker-toggle matSuffix [for]=\"endPicker\"></mat-datepicker-toggle>\n            <mat-datepicker #endPicker></mat-datepicker>\n          </mat-form-field>\n        </div>\n\n        <!-- Filter Action Buttons -->\n        <div class=\"filter-actions\">\n          <button mat-raised-button color=\"primary\" class=\"search-btn\" (click)=\"searchDashboard()\">\n            <mat-icon>search</mat-icon>\n            Search\n          </button>\n          <button mat-stroked-button class=\"reset-filters-btn\" (click)=\"resetFilters()\">\n            <mat-icon>refresh</mat-icon>\n            Reset\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Right Content Area -->\n    <div class=\"right-content\">\n      <!-- Top Search Bar -->\n      <div class=\"search-header\">\n        <div class=\"assistant-info\">\n          <mat-icon class=\"assistant-icon\">smart_toy</mat-icon>\n          <div class=\"assistant-text\">\n            <span class=\"assistant-title\">Smart Dashboard Assistant</span>\n            <span class=\"assistant-status\">Ready to analyze</span>\n          </div>\n        </div>\n        <div class=\"search-container\">\n          <mat-form-field appearance=\"outline\" class=\"search-field\">\n            <input matInput\n                   placeholder=\"Ask me about your business data\"\n                   [formControl]=\"searchQuery\"\n                   (keyup.enter)=\"onSearchQuery()\" />\n            <mat-icon matSuffix class=\"search-icon\" (click)=\"onSearchQuery()\">search</mat-icon>\n          </mat-form-field>\n        </div>\n      </div>\n\n      <!-- Dashboard Content Area -->\n      <div class=\"dashboard-content-area\">\n        <!-- Loading Spinner -->\n        <div *ngIf=\"isLoading\" class=\"loading-container\">\n          <mat-spinner diameter=\"50\"></mat-spinner>\n          <p>Loading dashboard data...</p>\n        </div>\n\n        <!-- Dashboard Grid -->\n        <div *ngIf=\"!isLoading && (summaryCards.length > 0 || charts.length > 0)\" class=\"dashboard-grid\">\n          <!-- Summary Cards Row -->\n          <div *ngIf=\"summaryCards.length > 0\" class=\"summary-cards-row\">\n            <mat-card *ngFor=\"let card of summaryCards\" class=\"summary-card\" [style.border-left-color]=\"card.color\">\n              <mat-card-content>\n                <div class=\"card-content\">\n                  <div class=\"card-icon\" [style.color]=\"card.color\">\n                    <mat-icon>{{card.icon}}</mat-icon>\n                  </div>\n                  <div class=\"card-info\">\n                    <div class=\"card-value\">{{card.value}}</div>\n                    <div class=\"card-label\">{{card.label}}</div>\n                  </div>\n                </div>\n              </mat-card-content>\n            </mat-card>\n          </div>\n\n          <!-- Charts Grid -->\n          <div *ngIf=\"charts.length > 0\" class=\"charts-grid\">\n            <mat-card *ngFor=\"let chart of charts; let i = index\"\n                      class=\"chart-card\"\n                      [ngClass]=\"getChartCssClass(chart)\">\n              <mat-card-header>\n                <mat-card-title class=\"chart-title\">{{chart.title}}</mat-card-title>\n              </mat-card-header>\n              <mat-card-content>\n                <div class=\"chart-container\" [attr.data-chart-type]=\"chart.type\">\n                  <!-- Dynamic Chart Rendering -->\n                  <canvas baseChart\n                          [type]=\"getChartType(chart)\"\n                          [data]=\"getChartData(chart)\"\n                          [options]=\"getChartOptions(chart)\">\n                  </canvas>\n                </div>\n              </mat-card-content>\n            </mat-card>\n          </div>\n        </div>\n\n        <!-- Single Unified Empty State -->\n        <div *ngIf=\"!isLoading && summaryCards.length === 0 && charts.length === 0\" class=\"empty-state\">\n          <mat-icon class=\"empty-icon\">analytics</mat-icon>\n          <h3>No Data Available</h3>\n          <p>Please select a location and date range to view dashboard data.</p>\n          <button mat-raised-button color=\"primary\" (click)=\"searchDashboard()\">\n            <mat-icon>refresh</mat-icon>\n            Refresh Data\n          </button>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,mBAAmB,QAAQ,wBAAwB;AAC5D,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,cAAc,QAAQ,YAAY;AAC3C,SAASC,wBAAwB,QAAQ,uBAAuB;AAChE,SAASC,WAAW,EAAEC,mBAAmB,EAAEC,WAAW,QAAQ,gBAAgB;AAC9E,SAASC,OAAO,EAAEC,SAAS,EAAEC,YAAY,EAAEC,oBAAoB,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;;;;ICLjEC,EAAA,CAAAC,cAAA,qBAA6C;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;;IACpEH,EAAA,CAAAC,cAAA,qBAAuF;IACrFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAF4CH,EAAA,CAAAI,UAAA,UAAAC,gBAAA,CAAAC,KAAA,CAA6B;IACpFN,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAH,gBAAA,CAAAI,KAAA,MACF;;;;;IA6BET,EAAA,CAAAC,cAAA,qBAAqF;IACnFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFuCH,EAAA,CAAAI,UAAA,UAAAM,UAAA,CAAAC,eAAA,CAAgC;IAClFX,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAE,UAAA,CAAAE,UAAA,MACF;;;;;IAcAZ,EAAA,CAAAC,cAAA,qBAA6C;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;;IACpEH,EAAA,CAAAC,cAAA,qBAA0F;IACxFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAF8CH,EAAA,CAAAI,UAAA,UAAAS,kBAAA,CAAAP,KAAA,CAA8B;IACvFN,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAK,kBAAA,CAAAJ,KAAA,MACF;;;;;IAXNT,EAAA,CAAAC,cAAA,cAAmE;IAErDD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1BH,EAAA,CAAAE,MAAA,kBACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,yBAA0D;IAC7CD,EAAA,CAAAE,MAAA,uBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACvCH,EAAA,CAAAC,cAAA,qBAAsE;IACpED,EAAA,CAAAc,UAAA,IAAAC,oDAAA,wBAAoE;IACpEf,EAAA,CAAAc,UAAA,KAAAE,qDAAA,wBAEa;IACfhB,EAAA,CAAAG,YAAA,EAAa;;;;IALDH,EAAA,CAAAO,SAAA,GAA4B;IAA5BP,EAAA,CAAAI,UAAA,gBAAAa,MAAA,CAAAC,YAAA,CAA4B,cAAAD,MAAA,CAAAE,cAAA;IACzBnB,EAAA,CAAAO,SAAA,GAAqB;IAArBP,EAAA,CAAAI,UAAA,UAAAa,MAAA,CAAAE,cAAA,CAAqB;IACKnB,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAI,UAAA,YAAAa,MAAA,CAAAG,eAAA,CAAkB;;;;;IA0E/DpB,EAAA,CAAAC,cAAA,cAAiD;IAC/CD,EAAA,CAAAqB,SAAA,sBAAyC;IACzCrB,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,gCAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAO9BH,EAAA,CAAAC,cAAA,mBAAwG;IAItFD,EAAA,CAAAE,MAAA,GAAa;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAEpCH,EAAA,CAAAC,cAAA,cAAuB;IACGD,EAAA,CAAAE,MAAA,GAAc;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC5CH,EAAA,CAAAC,cAAA,cAAwB;IAAAD,EAAA,CAAAE,MAAA,IAAc;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IARaH,EAAA,CAAAsB,WAAA,sBAAAC,QAAA,CAAAC,KAAA,CAAsC;IAG1ExB,EAAA,CAAAO,SAAA,GAA0B;IAA1BP,EAAA,CAAAsB,WAAA,UAAAC,QAAA,CAAAC,KAAA,CAA0B;IACrCxB,EAAA,CAAAO,SAAA,GAAa;IAAbP,EAAA,CAAAyB,iBAAA,CAAAF,QAAA,CAAAG,IAAA,CAAa;IAGC1B,EAAA,CAAAO,SAAA,GAAc;IAAdP,EAAA,CAAAyB,iBAAA,CAAAF,QAAA,CAAAjB,KAAA,CAAc;IACdN,EAAA,CAAAO,SAAA,GAAc;IAAdP,EAAA,CAAAyB,iBAAA,CAAAF,QAAA,CAAAd,KAAA,CAAc;;;;;IAThDT,EAAA,CAAAC,cAAA,cAA+D;IAC7DD,EAAA,CAAAc,UAAA,IAAAa,wDAAA,wBAYW;IACb3B,EAAA,CAAAG,YAAA,EAAM;;;;IAbuBH,EAAA,CAAAO,SAAA,GAAe;IAAfP,EAAA,CAAAI,UAAA,YAAAwB,OAAA,CAAAC,YAAA,CAAe;;;;;IAiB1C7B,EAAA,CAAAC,cAAA,mBAE8C;IAEND,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAEtEH,EAAA,CAAAC,cAAA,uBAAkB;IAGdD,EAAA,CAAAqB,SAAA,iBAIS;IACXrB,EAAA,CAAAG,YAAA,EAAM;;;;;IAZAH,EAAA,CAAAI,UAAA,YAAA0B,OAAA,CAAAC,gBAAA,CAAAC,SAAA,EAAmC;IAELhC,EAAA,CAAAO,SAAA,GAAe;IAAfP,EAAA,CAAAyB,iBAAA,CAAAO,SAAA,CAAAC,KAAA,CAAe;IAGtBjC,EAAA,CAAAO,SAAA,GAAmC;IAAnCP,EAAA,CAAAkC,WAAA,oBAAAF,SAAA,CAAAG,IAAA,CAAmC;IAGtDnC,EAAA,CAAAO,SAAA,GAA4B;IAA5BP,EAAA,CAAAI,UAAA,SAAA0B,OAAA,CAAAM,YAAA,CAAAJ,SAAA,EAA4B,SAAAF,OAAA,CAAAO,YAAA,CAAAL,SAAA,cAAAF,OAAA,CAAAQ,eAAA,CAAAN,SAAA;;;;;IAX5ChC,EAAA,CAAAC,cAAA,cAAmD;IACjDD,EAAA,CAAAc,UAAA,IAAAyB,wDAAA,uBAgBW;IACbvC,EAAA,CAAAG,YAAA,EAAM;;;;IAjBwBH,EAAA,CAAAO,SAAA,GAAW;IAAXP,EAAA,CAAAI,UAAA,YAAAoC,OAAA,CAAAC,MAAA,CAAW;;;;;IApB3CzC,EAAA,CAAAC,cAAA,cAAiG;IAE/FD,EAAA,CAAAc,UAAA,IAAA4B,6CAAA,kBAcM;IAGN1C,EAAA,CAAAc,UAAA,IAAA6B,6CAAA,kBAkBM;IACR3C,EAAA,CAAAG,YAAA,EAAM;;;;IApCEH,EAAA,CAAAO,SAAA,GAA6B;IAA7BP,EAAA,CAAAI,UAAA,SAAAwC,MAAA,CAAAf,YAAA,CAAAgB,MAAA,KAA6B;IAiB7B7C,EAAA,CAAAO,SAAA,GAAuB;IAAvBP,EAAA,CAAAI,UAAA,SAAAwC,MAAA,CAAAH,MAAA,CAAAI,MAAA,KAAuB;;;;;;IAsB/B7C,EAAA,CAAAC,cAAA,cAAgG;IACjED,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACjDH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC1BH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,sEAA+D;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACtEH,EAAA,CAAAC,cAAA,iBAAsE;IAA5BD,EAAA,CAAA8C,UAAA,mBAAAC,gEAAA;MAAA/C,EAAA,CAAAgD,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAlD,EAAA,CAAAmD,aAAA;MAAA,OAASnD,EAAA,CAAAoD,WAAA,CAAAF,OAAA,CAAAG,eAAA,EAAiB;IAAA,EAAC;IACnErD,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC5BH,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;AD3JnB,MAuBamD,uBAAuB;EA+BlCC,YACUC,qBAA4C,EAC5CC,WAAwB,EACxBC,gBAAkC,EAClCC,aAAqC,EACrCC,aAAmC,EACnCC,GAAsB;IALtB,KAAAL,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,GAAG,GAAHA,GAAG;IApCL,KAAAC,QAAQ,GAAG,IAAIlE,OAAO,EAAQ;IAItC,KAAAmE,QAAQ,GAAU,EAAE;IACpB,KAAAC,gBAAgB,GAAU,EAAE;IAC5B,KAAAC,iBAAiB,GAAa,EAAE;IAEhC;IACA,KAAAC,kBAAkB,GAAG,IAAIzE,WAAW,CAAC,EAAE,CAAC;IACxC,KAAA0E,SAAS,GAAG,IAAI1E,WAAW,EAAE;IAC7B,KAAA2E,OAAO,GAAG,IAAI3E,WAAW,EAAE;IAC3B,KAAA4E,WAAW,GAAG,IAAI5E,WAAW,CAAC,EAAE,CAAC;IACjC,KAAAyB,YAAY,GAAG,IAAIzB,WAAW,EAAE;IAChC,KAAA6E,iBAAiB,GAAG,EAAE;IAEtB;IACA,KAAAzC,YAAY,GAAkB,EAAE;IAChC,KAAAY,MAAM,GAAiB,EAAE;IACzB,KAAA8B,SAAS,GAAG,KAAK;IACjB,KAAApD,cAAc,GAAG,KAAK;IAEtB;IACA,KAAAqD,cAAc,GAAoB,EAAE;IACpC,KAAApD,eAAe,GAAqB,EAAE;IAcpC,IAAI,CAACqD,IAAI,GAAG,IAAI,CAAChB,WAAW,CAACiB,cAAc,EAAE;IAC7C,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEQA,gBAAgBA,CAAA;IACtB;IACA,IAAI,CAAChB,aAAa,CAACiB,UAAU,EAAE,CAACC,SAAS,CAAC;MACxCC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,MAAM,KAAK,SAAS,EAAE;UACjC,IAAI,CAACrB,aAAa,CAACsB,SAAS,CAACF,QAAQ,CAACG,IAAI,CAAC;UAC3C,IAAI,CAACC,0BAA0B,CAACJ,QAAQ,CAACG,IAAI,CAAC;SAC/C,MAAM;UACL,IAAI,CAACE,0BAA0B,EAAE;;QAEnC,IAAI,CAACjE,cAAc,GAAG,IAAI;QAC1B,IAAI,CAAC0C,GAAG,CAACwB,aAAa,EAAE;MAC1B,CAAC;MACDC,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAACF,0BAA0B,EAAE;QACjC,IAAI,CAACjE,cAAc,GAAG,IAAI;QAC1B,IAAI,CAAC0C,GAAG,CAACwB,aAAa,EAAE;MAC1B;KACD,CAAC;EACJ;EAEQF,0BAA0BA,CAACI,MAAW;IAC5C;IACA,IAAI,CAACf,cAAc,GAAGe,MAAM,CAACC,eAAe,IAAI,EAAE;IAElD;IACA,IAAI,CAACpE,eAAe,GAAGmE,MAAM,CAACE,iBAAiB,IAAI,EAAE;IAErD;IACA,MAAMC,QAAQ,GAAGH,MAAM,CAACI,SAAS,IAAI,EAAE;IAEvC;IACA,IAAI,CAACrB,iBAAiB,GAAGoB,QAAQ,CAACE,sBAAsB,IAAI,UAAU;IACtE,IAAI,CAAC1E,YAAY,CAAC2E,QAAQ,CAACH,QAAQ,CAACI,iBAAiB,IAAI,cAAc,CAAC;IAExE;IACA,IAAI,CAACC,mBAAmB,EAAE;IAE1B;IACA;EACF;;EAEQX,0BAA0BA,CAAA;IAChC;IACA,IAAI,CAACZ,cAAc,GAAG,CACpB;MAAElE,KAAK,EAAE,UAAU;MAAEG,KAAK,EAAE;IAAoB,CAAE,EAClD;MAAEH,KAAK,EAAE,WAAW;MAAEG,KAAK,EAAE;IAAqB,CAAE,CACrD;IACD,IAAI,CAACW,eAAe,GAAG,CACrB;MAAEd,KAAK,EAAE,cAAc;MAAEG,KAAK,EAAE;IAAe,CAAE,EACjD;MAAEH,KAAK,EAAE,WAAW;MAAEG,KAAK,EAAE;IAAY,CAAE,EAC3C;MAAEH,KAAK,EAAE,aAAa;MAAEG,KAAK,EAAE;IAAc,CAAE,CAChD;IACD,IAAI,CAAC6D,iBAAiB,GAAG,WAAW;IACpC,IAAI,CAACpD,YAAY,CAAC2E,QAAQ,CAAC,cAAc,CAAC;IAE1C;IACA,IAAI,CAACE,mBAAmB,EAAE;IAE1B;IACA;EACF;;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,iBAAiB,EAAE;IACxB,IAAI,CAACC,YAAY,EAAE;IACnB;IACA;EACF;;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACrC,QAAQ,CAACgB,IAAI,EAAE;IACpB,IAAI,CAAChB,QAAQ,CAACsC,QAAQ,EAAE;EAC1B;EAEQH,iBAAiBA,CAAA;IACvB;IACA,IAAI,CAAC/B,kBAAkB,CAACmC,YAAY,CACjCC,IAAI,CACHxG,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBF,SAAS,CAAC,IAAI,CAACiE,QAAQ,CAAC,CACzB,CACAe,SAAS,CAAEvE,KAAoB,IAAI;MAClC,IAAI,CAACiG,cAAc,CAACjG,KAAK,IAAI,EAAE,CAAC;IAClC,CAAC,CAAC;EACN;EAEQ4F,YAAYA,CAAA;IAClB,IAAI,CAACxC,gBAAgB,CAAC8C,sBAAsB,CACzCF,IAAI,CAACzG,SAAS,CAAC,IAAI,CAACiE,QAAQ,CAAC,CAAC,CAC9Be,SAAS,CAACK,IAAI,IAAG;MAChB,IAAI,CAACnB,QAAQ,GAAGmB,IAAI,IAAI,EAAE;MAC1B,IAAI,CAAClB,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAACD,QAAQ,CAAC;MAE1C;MACA,IAAI,CAACE,iBAAiB,GAAG,IAAI,CAACF,QAAQ,CAAC0C,GAAG,CAACC,MAAM,IAAIA,MAAM,CAAC/F,eAAe,CAAC;IAC9E,CAAC,CAAC;EACN;EAEQ4F,cAAcA,CAACI,UAAkB;IACvC,IAAI,CAACA,UAAU,EAAE;MACf,IAAI,CAAC3C,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAACD,QAAQ,CAAC;KAC3C,MAAM;MACL,MAAM6C,oBAAoB,GAAGD,UAAU,CAACE,WAAW,EAAE,CAACC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;MACxE,IAAI,CAAC9C,gBAAgB,GAAG,IAAI,CAACD,QAAQ,CAACgD,MAAM,CAACL,MAAM,IACjDA,MAAM,CAAC9F,UAAU,CAACiG,WAAW,EAAE,CAACC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAACE,QAAQ,CAACJ,oBAAoB,CAAC,CAClF;;EAEL;EAEAK,iBAAiBA,CAAA;IACf,IAAI,CAAC,IAAI,CAAC9C,SAAS,CAAC7D,KAAK,IAAI,CAAC,IAAI,CAAC8D,OAAO,CAAC9D,KAAK,IAAI,CAAC,IAAI,CAACgE,iBAAiB,EAAE;MAC3E;;IAGF,IAAI,CAACC,SAAS,GAAG,IAAI;IAErB,MAAM2C,OAAO,GAAG;MACdC,SAAS,EAAE,IAAI,CAAClD,iBAAiB,IAAI,EAAE;MACvCE,SAAS,EAAE,IAAI,CAACiD,UAAU,CAAC,IAAI,CAACjD,SAAS,CAAC7D,KAAK,CAAC;MAChD8D,OAAO,EAAE,IAAI,CAACgD,UAAU,CAAC,IAAI,CAAChD,OAAO,CAAC9D,KAAK,CAAC;MAC5C+G,QAAQ,EAAE,IAAI,CAACnG,YAAY,CAACZ,KAAK,IAAI;KACtC;IAED,MAAMgH,OAAO,GAAG;MACdC,SAAS,EAAE,IAAI,CAAC9C,IAAI,CAAC+C,QAAQ;MAC7BN,OAAO,EAAEA,OAAO;MAChBO,UAAU,EAAE,EAAE;MACdC,kBAAkB,EAAE,IAAI;MACxBC,cAAc,EAAE,IAAI,CAACrD;KACtB;IAED,IAAI,CAACd,qBAAqB,CAACoE,qBAAqB,CAACN,OAAO,CAAC,CACtDhB,IAAI,CAACzG,SAAS,CAAC,IAAI,CAACiE,QAAQ,CAAC,CAAC,CAC9Be,SAAS,CAAC;MACTC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,MAAM,KAAK,SAAS,EAAE;UACjC,IAAI,CAAC6C,oBAAoB,CAAC9C,QAAQ,CAACG,IAAI,CAAC;SACzC,MAAM;UACL,IAAI,CAAC4C,kBAAkB,EAAE;;QAE3B,IAAI,CAACvD,SAAS,GAAG,KAAK;QACtB,IAAI,CAACV,GAAG,CAACwB,aAAa,EAAE;MAC1B,CAAC;MACDC,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAACwC,kBAAkB,EAAE;QACzB,IAAI,CAACvD,SAAS,GAAG,KAAK;QACtB,IAAI,CAACV,GAAG,CAACwB,aAAa,EAAE;MAC1B;KACD,CAAC;EACN;EAEQyC,kBAAkBA,CAAA;IACxB,IAAI,CAACjG,YAAY,GAAG,EAAE;IACtB,IAAI,CAACY,MAAM,GAAG,EAAE;EAClB;EAEQoF,oBAAoBA,CAAC3C,IAAS;IACpC;IACA,IAAI,CAACrD,YAAY,GAAGqD,IAAI,CAAC6C,aAAa,EAAEtB,GAAG,CAAEuB,IAAS,KAAM;MAC1DtG,IAAI,EAAEsG,IAAI,CAACtG,IAAI,IAAI,IAAI,CAACiC,aAAa,CAACsE,kBAAkB,CAACD,IAAI,CAACE,SAAS,CAAC;MACxE5H,KAAK,EAAE0H,IAAI,CAAC1H,KAAK;MACjBG,KAAK,EAAEuH,IAAI,CAACvH,KAAK;MACjBe,KAAK,EAAE,IAAI,CAACmC,aAAa,CAACwE,mBAAmB,CAACH,IAAI,CAACE,SAAS,CAAC;MAC7DA,SAAS,EAAEF,IAAI,CAACE;KACjB,CAAC,CAAC,IAAI,EAAE;IAET;IACA,IAAI,CAACzF,MAAM,GAAGyC,IAAI,CAACzC,MAAM,EAAEgE,GAAG,CAAE2B,KAAU,IACxC,IAAI,CAACxE,aAAa,CAACyE,YAAY,CAACD,KAAK,CAAC,CACvC,IAAI,EAAE;EACT;EAEQhB,UAAUA,CAACkB,IAAU;IAC3B;IACA,MAAMC,IAAI,GAAGD,IAAI,CAACE,WAAW,EAAE;IAC/B,MAAMC,KAAK,GAAGC,MAAM,CAACJ,IAAI,CAACK,QAAQ,EAAE,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC1D,MAAMC,GAAG,GAAGH,MAAM,CAACJ,IAAI,CAACQ,OAAO,EAAE,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACnD,OAAO,GAAGL,IAAI,IAAIE,KAAK,IAAII,GAAG,EAAE;EAClC;EAEQ9C,mBAAmBA,CAAA;IACzB,MAAMgD,KAAK,GAAG,IAAIC,IAAI,EAAE;IAExB,IAAI,IAAI,CAAC1E,iBAAiB,KAAK,WAAW,EAAE;MAC1C;MACA,MAAM2E,YAAY,GAAG,IAAID,IAAI,CAACD,KAAK,CAACP,WAAW,EAAE,EAAEO,KAAK,CAACJ,QAAQ,EAAE,EAAE,CAAC,CAAC;MACvE,IAAI,CAACxE,SAAS,CAAC0B,QAAQ,CAACoD,YAAY,CAAC;MACrC,IAAI,CAAC7E,OAAO,CAACyB,QAAQ,CAACkD,KAAK,CAAC;KAC7B,MAAM;MACL;MACA,MAAMG,aAAa,GAAG,IAAIF,IAAI,CAACD,KAAK,CAACI,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;MAC1E,IAAI,CAAChF,SAAS,CAAC0B,QAAQ,CAACqD,aAAa,CAAC;MACtC,IAAI,CAAC9E,OAAO,CAACyB,QAAQ,CAACkD,KAAK,CAAC;;EAEhC;EAEAK,gBAAgBA,CAAA;IACd;EAAA;EAGFC,YAAYA,CAAA;IACV;EAAA;EAGFC,iBAAiBA,CAAA;IACf;IACA,IAAI,CAACvD,mBAAmB,EAAE;IAC1B;EACF;;EAEA1C,eAAeA,CAAA;IACb;IACA,IAAI,CAAC4D,iBAAiB,EAAE;EAC1B;EAEAsC,YAAYA,CAAA;IACV;IACA,IAAI,CAACtF,iBAAiB,GAAG,IAAI,CAACF,QAAQ,CAAC0C,GAAG,CAACC,MAAM,IAAIA,MAAM,CAAC/F,eAAe,CAAC;IAC5E,IAAI,CAACoF,mBAAmB,EAAE;IAC1B,IAAI,CAAC7E,YAAY,CAAC2E,QAAQ,CAAC,cAAc,CAAC;IAC1C,IAAI,CAACxB,WAAW,CAACwB,QAAQ,CAAC,EAAE,CAAC;IAC7B,IAAI,CAAC3B,kBAAkB,CAAC2B,QAAQ,CAAC,EAAE,CAAC;IACpC,IAAI,CAAC7B,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAACD,QAAQ,CAAC;IAE1C;IACA,IAAI,CAACkD,iBAAiB,EAAE;EAC1B;EAEAuC,aAAaA,CAAA;IACX,MAAMC,KAAK,GAAG,IAAI,CAACpF,WAAW,CAAC/D,KAAK,EAAEoJ,IAAI,EAAE;IAC5C,IAAID,KAAK,EAAE;MACT,IAAI,CAACE,0BAA0B,CAACF,KAAK,CAAC;KACvC,MAAM;MACL,IAAI,CAACxC,iBAAiB,EAAE;;EAE5B;EAEQ0C,0BAA0BA,CAACF,KAAa;IAC9C,IAAI,CAAC,IAAI,CAACtF,SAAS,CAAC7D,KAAK,IAAI,CAAC,IAAI,CAAC8D,OAAO,CAAC9D,KAAK,IAAI,CAAC,IAAI,CAACgE,iBAAiB,EAAE;MAC3E;;IAGF,IAAI,CAACC,SAAS,GAAG,IAAI;IAErB,MAAM2C,OAAO,GAAG;MACdC,SAAS,EAAE,IAAI,CAAClD,iBAAiB,IAAI,EAAE;MACvCE,SAAS,EAAE,IAAI,CAACiD,UAAU,CAAC,IAAI,CAACjD,SAAS,CAAC7D,KAAK,CAAC;MAChD8D,OAAO,EAAE,IAAI,CAACgD,UAAU,CAAC,IAAI,CAAChD,OAAO,CAAC9D,KAAK,CAAC;MAC5C+G,QAAQ,EAAE,IAAI,CAACnG,YAAY,CAACZ,KAAK,IAAI;KACtC;IAED,MAAMgH,OAAO,GAAG;MACdC,SAAS,EAAE,IAAI,CAAC9C,IAAI,CAAC+C,QAAQ;MAC7BN,OAAO,EAAEA,OAAO;MAChBO,UAAU,EAAEgC,KAAK;MACjB/B,kBAAkB,EAAE,KAAK;MACzBC,cAAc,EAAE,IAAI,CAACrD;KACtB;IAED,IAAI,CAACd,qBAAqB,CAACoE,qBAAqB,CAACN,OAAO,CAAC,CACtDhB,IAAI,CAACzG,SAAS,CAAC,IAAI,CAACiE,QAAQ,CAAC,CAAC,CAC9Be,SAAS,CAAC;MACTC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,MAAM,KAAK,SAAS,EAAE;UACjC,IAAI,CAAC6C,oBAAoB,CAAC9C,QAAQ,CAACG,IAAI,CAAC;SACzC,MAAM;UACL,IAAI,CAAC4C,kBAAkB,EAAE;;QAE3B,IAAI,CAACvD,SAAS,GAAG,KAAK;QACtB,IAAI,CAACV,GAAG,CAACwB,aAAa,EAAE;MAC1B,CAAC;MACDC,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAACwC,kBAAkB,EAAE;QACzB,IAAI,CAACvD,SAAS,GAAG,KAAK;QACtB,IAAI,CAACV,GAAG,CAACwB,aAAa,EAAE;MAC1B;KACD,CAAC;EACN;EAEA;EACAhD,YAAYA,CAAC+F,KAAiB;IAC5B,OAAOA,KAAK,CAAClD,IAAI;EACnB;EAEA9C,YAAYA,CAACgG,KAAiB;IAC5B,OAAO,IAAI,CAACxE,aAAa,CAACxB,YAAY,CAACgG,KAAK,CAACjG,IAAI,CAAC;EACpD;EAEAG,eAAeA,CAAC8F,KAAiB;IAC/B,OAAOA,KAAK,CAACwB,OAAO,IAAI,IAAI,CAACjG,aAAa,CAACkG,sBAAsB,EAAE;EACrE;EAEA9H,gBAAgBA,CAACqG,KAAiB;IAChC,OAAO,IAAI,CAACxE,aAAa,CAAC7B,gBAAgB,CAACqG,KAAK,CAAC;EACnD;;;uBAnVW9E,uBAAuB,EAAAtD,EAAA,CAAA8J,iBAAA,CAAAC,EAAA,CAAAC,qBAAA,GAAAhK,EAAA,CAAA8J,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAlK,EAAA,CAAA8J,iBAAA,CAAAK,EAAA,CAAAC,gBAAA,GAAApK,EAAA,CAAA8J,iBAAA,CAAAO,EAAA,CAAAC,sBAAA,GAAAtK,EAAA,CAAA8J,iBAAA,CAAAS,EAAA,CAAAC,oBAAA,GAAAxK,EAAA,CAAA8J,iBAAA,CAAA9J,EAAA,CAAAyK,iBAAA;IAAA;EAAA;;;YAAvBnH,uBAAuB;MAAAoH,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAA5K,EAAA,CAAA6K,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCvDpCnL,EAAA,CAAAC,cAAA,aAAuC;UAQlBD,EAAA,CAAAE,MAAA,uBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACvCH,EAAA,CAAAC,cAAA,oBAAuE;UAA3DD,EAAA,CAAA8C,UAAA,yBAAAuI,mEAAAC,MAAA;YAAA,OAAAF,GAAA,CAAA9G,iBAAA,GAAAgH,MAAA;UAAA,EAA6B;UACvCtL,EAAA,CAAAc,UAAA,IAAAyK,6CAAA,wBAAoE;UACpEvL,EAAA,CAAAc,UAAA,IAAA0K,6CAAA,wBAEa;UACfxL,EAAA,CAAAG,YAAA,EAAa;UAIjBH,EAAA,CAAAC,cAAA,cAA6B;UAEfD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACzBH,EAAA,CAAAE,MAAA,uBACA;UAAAF,EAAA,CAAAC,cAAA,gBAA2B;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAIrCH,EAAA,CAAAC,cAAA,eAA0B;UAEZD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC/BH,EAAA,CAAAE,MAAA,qBACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,0BAA0D;UAC7CD,EAAA,CAAAE,MAAA,0BAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACzCH,EAAA,CAAAC,cAAA,sBAAmD;UAAvCD,EAAA,CAAA8C,UAAA,yBAAA2I,oEAAAH,MAAA;YAAA,OAAAF,GAAA,CAAAnH,iBAAA,GAAAqH,MAAA;UAAA,EAA6B;UACvCtL,EAAA,CAAAC,cAAA,kBAAY;UACVD,EAAA,CAAAqB,SAAA,iCAIwB;UAC1BrB,EAAA,CAAAG,YAAA,EAAa;UAEbH,EAAA,CAAAc,UAAA,KAAA4K,8CAAA,wBAEa;UACf1L,EAAA,CAAAG,YAAA,EAAa;UAKjBH,EAAA,CAAAc,UAAA,KAAA6K,uCAAA,mBAcM;UAGN3L,EAAA,CAAAC,cAAA,eAA0B;UAEZD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC/BH,EAAA,CAAAE,MAAA,oBACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,0BAA0D;UAC7CD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACjCH,EAAA,CAAAqB,SAAA,iBAAwE;UAG1ErB,EAAA,CAAAG,YAAA,EAAiB;UAInBH,EAAA,CAAAC,cAAA,eAA0B;UAEZD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC/BH,EAAA,CAAAE,MAAA,kBACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,0BAA0D;UAC7CD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC/BH,EAAA,CAAAqB,SAAA,iBAAoE;UAGtErB,EAAA,CAAAG,YAAA,EAAiB;UAInBH,EAAA,CAAAC,cAAA,eAA4B;UACmCD,EAAA,CAAA8C,UAAA,mBAAA8I,0DAAA;YAAA,OAASR,GAAA,CAAA/H,eAAA,EAAiB;UAAA,EAAC;UACtFrD,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC3BH,EAAA,CAAAE,MAAA,gBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAA8E;UAAzBD,EAAA,CAAA8C,UAAA,mBAAA+I,0DAAA;YAAA,OAAST,GAAA,CAAA7B,YAAA,EAAc;UAAA,EAAC;UAC3EvJ,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC5BH,EAAA,CAAAE,MAAA,eACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAMfH,EAAA,CAAAC,cAAA,eAA2B;UAIYD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACrDH,EAAA,CAAAC,cAAA,eAA4B;UACID,EAAA,CAAAE,MAAA,iCAAyB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC9DH,EAAA,CAAAC,cAAA,gBAA+B;UAAAD,EAAA,CAAAE,MAAA,wBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAG1DH,EAAA,CAAAC,cAAA,eAA8B;UAKnBD,EAAA,CAAA8C,UAAA,yBAAAgJ,+DAAA;YAAA,OAAeV,GAAA,CAAA5B,aAAA,EAAe;UAAA,EAAC;UAHtCxJ,EAAA,CAAAG,YAAA,EAGyC;UACzCH,EAAA,CAAAC,cAAA,oBAAkE;UAA1BD,EAAA,CAAA8C,UAAA,mBAAAiJ,4DAAA;YAAA,OAASX,GAAA,CAAA5B,aAAA,EAAe;UAAA,EAAC;UAACxJ,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAMzFH,EAAA,CAAAC,cAAA,eAAoC;UAElCD,EAAA,CAAAc,UAAA,KAAAkL,uCAAA,kBAGM;UAGNhM,EAAA,CAAAc,UAAA,KAAAmL,uCAAA,kBAsCM;UAGNjM,EAAA,CAAAc,UAAA,KAAAoL,uCAAA,mBAQM;UACRlM,EAAA,CAAAG,YAAA,EAAM;;;;;UApLUH,EAAA,CAAAO,SAAA,GAA6B;UAA7BP,EAAA,CAAAI,UAAA,UAAAgL,GAAA,CAAA9G,iBAAA,CAA6B,cAAA8G,GAAA,CAAAjK,cAAA;UAC1BnB,EAAA,CAAAO,SAAA,GAAqB;UAArBP,EAAA,CAAAI,UAAA,UAAAgL,GAAA,CAAAjK,cAAA,CAAqB;UACInB,EAAA,CAAAO,SAAA,GAAiB;UAAjBP,EAAA,CAAAI,UAAA,YAAAgL,GAAA,CAAA5G,cAAA,CAAiB;UAsB3CxE,EAAA,CAAAO,SAAA,IAA6B;UAA7BP,EAAA,CAAAI,UAAA,UAAAgL,GAAA,CAAAnH,iBAAA,CAA6B;UAGnCjE,EAAA,CAAAO,SAAA,GAAkC;UAAlCP,EAAA,CAAAI,UAAA,gBAAAgL,GAAA,CAAAlH,kBAAA,CAAkC;UAMPlE,EAAA,CAAAO,SAAA,GAAmB;UAAnBP,EAAA,CAAAI,UAAA,YAAAgL,GAAA,CAAApH,gBAAA,CAAmB;UAQ7BhE,EAAA,CAAAO,SAAA,GAAsC;UAAtCP,EAAA,CAAAI,UAAA,SAAAgL,GAAA,CAAA9G,iBAAA,gBAAsC;UAwB7CtE,EAAA,CAAAO,SAAA,GAA6B;UAA7BP,EAAA,CAAAI,UAAA,kBAAA+L,GAAA,CAA6B,gBAAAf,GAAA,CAAAjH,SAAA;UACZnE,EAAA,CAAAO,SAAA,GAAmB;UAAnBP,EAAA,CAAAI,UAAA,QAAA+L,GAAA,CAAmB;UAapCnM,EAAA,CAAAO,SAAA,IAA2B;UAA3BP,EAAA,CAAAI,UAAA,kBAAAgM,GAAA,CAA2B,gBAAAhB,GAAA,CAAAhH,OAAA;UACVpE,EAAA,CAAAO,SAAA,GAAiB;UAAjBP,EAAA,CAAAI,UAAA,QAAAgM,GAAA,CAAiB;UAkC3CpM,EAAA,CAAAO,SAAA,IAA2B;UAA3BP,EAAA,CAAAI,UAAA,gBAAAgL,GAAA,CAAA/G,WAAA,CAA2B;UAUhCrE,EAAA,CAAAO,SAAA,GAAe;UAAfP,EAAA,CAAAI,UAAA,SAAAgL,GAAA,CAAA7G,SAAA,CAAe;UAMfvE,EAAA,CAAAO,SAAA,GAAkE;UAAlEP,EAAA,CAAAI,UAAA,UAAAgL,GAAA,CAAA7G,SAAA,KAAA6G,GAAA,CAAAvJ,YAAA,CAAAgB,MAAA,QAAAuI,GAAA,CAAA3I,MAAA,CAAAI,MAAA,MAAkE;UAyClE7C,EAAA,CAAAO,SAAA,GAAoE;UAApEP,EAAA,CAAAI,UAAA,UAAAgL,GAAA,CAAA7G,SAAA,IAAA6G,GAAA,CAAAvJ,YAAA,CAAAgB,MAAA,UAAAuI,GAAA,CAAA3I,MAAA,CAAAI,MAAA,OAAoE;;;qBDhJ9EjE,YAAY,EAAAyN,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EACZ3N,aAAa,EAAA4N,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,cAAA,EAAAF,EAAA,CAAAG,aAAA,EAAAH,EAAA,CAAAI,YAAA,EACb/N,eAAe,EAAAgO,EAAA,CAAAC,SAAA,EACfhO,aAAa,EAAAiO,EAAA,CAAAC,OAAA,EACbjO,eAAe,EAAAkO,GAAA,CAAAC,YAAA,EAAAD,GAAA,CAAAE,QAAA,EAAAF,GAAA,CAAAG,SAAA,EAAAC,GAAA,CAAAC,SAAA,EAAAC,GAAA,CAAAC,SAAA,EACfxO,kBAAkB,EAClBC,cAAc,EAAAwO,GAAA,CAAAC,QAAA,EACdxO,mBAAmB,EAAAyO,GAAA,CAAAC,aAAA,EAAAD,GAAA,CAAAE,kBAAA,EAAAF,GAAA,CAAAG,mBAAA,EACnB3O,mBAAmB,EACnBC,gBAAgB,EAChBC,wBAAwB,EAAA0O,GAAA,CAAAC,kBAAA,EACxB1O,cAAc,EAAA2O,GAAA,CAAAC,kBAAA,EACd3O,wBAAwB,EAAA4O,GAAA,CAAAC,wBAAA,EACxB3O,mBAAmB,EAAA4O,GAAA,CAAAC,oBAAA,EAAAD,GAAA,CAAAE,eAAA,EAAAF,GAAA,CAAAG,oBAAA,EACnB9O,WAAW;MAAA+O,MAAA;IAAA;EAAA;;SAKFpL,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}