import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, BehaviorSubject } from 'rxjs';
import { environment } from '../../environments/environment';

export interface DashboardType {
  value: string;
  label: string;
}

export interface BaseDateOption {
  value: string;
  label: string;
}

export interface DashboardConfig {
  chart_colors: string[];
  chart_types: { [key: string]: string };
  currency: {
    code: string;
    symbol: string;
  };
  dashboard_types: DashboardType[];
  base_date_options: BaseDateOption[];
  default_chart_options: any;
  summary_card_config: {
    colors: { [key: string]: string };
    icons: { [key: string]: string };
  };
  ui_config: {
    default_date_range_days: number;
    default_dashboard_type: string;
    default_base_date: string;
  };
}

export interface DashboardConfigResponse {
  status: string;
  data: DashboardConfig;
}

@Injectable({
  providedIn: 'root'
})
export class DashboardConfigService {
  private baseUrl: string = environment.engineUrl;
  private configSubject = new BehaviorSubject<DashboardConfig | null>(null);
  public config$ = this.configSubject.asObservable();

  constructor(private http: HttpClient) {}

  /**
   * Load dashboard configuration from backend
   */
  loadConfig(): Observable<DashboardConfigResponse> {
    return this.http.get<DashboardConfigResponse>(`${this.baseUrl}api/smart-dashboard/config`);
  }

  /**
   * Set configuration and notify subscribers
   */
  setConfig(config: DashboardConfig): void {
    this.configSubject.next(config);
  }

  /**
   * Get current configuration
   */
  getCurrentConfig(): DashboardConfig | null {
    return this.configSubject.value;
  }

  /**
   * Get chart colors from config
   */
  getChartColors(): string[] {
    const config = this.getCurrentConfig();
    return config?.chart_colors || [];
  }

  /**
   * Get currency symbol from config
   */
  getCurrencySymbol(): string {
    const config = this.getCurrentConfig();
    return config?.currency?.symbol || '₹';
  }

  /**
   * Get summary card color based on data type
   */
  getSummaryCardColor(dataType: string): string {
    const config = this.getCurrentConfig();
    return config?.summary_card_config?.colors?.[dataType] || '#ffb366';
  }

  /**
   * Get summary card icon based on data type
   */
  getSummaryCardIcon(dataType: string): string {
    const config = this.getCurrentConfig();
    return config?.summary_card_config?.icons?.[dataType] || 'analytics';
  }

  /**
   * Get default chart options from config
   */
  getDefaultChartOptions(): any {
    const config = this.getCurrentConfig();
    return config?.default_chart_options || {
      responsive: true,
      maintainAspectRatio: false
    };
  }

  /**
   * Get available dashboard types
   */
  getDashboardTypes(): DashboardType[] {
    const config = this.getCurrentConfig();
    return config?.dashboard_types || [];
  }

  /**
   * Get available base date options
   */
  getBaseDateOptions(): BaseDateOption[] {
    const config = this.getCurrentConfig();
    return config?.base_date_options || [];
  }

  /**
   * Get UI configuration defaults
   */
  getUIConfig(): any {
    const config = this.getCurrentConfig();
    return config?.ui_config || {
      default_date_range_days: 30,
      default_dashboard_type: 'inventory',
      default_base_date: 'deliveryDate'
    };
  }

  /**
   * Get chart type display name
   */
  getChartTypeDisplayName(type: string): string {
    const config = this.getCurrentConfig();
    return config?.chart_types?.[type] || type;
  }

  /**
   * Merge chart options with defaults
   */
  mergeChartOptions(chartOptions: any): any {
    const defaultOptions = this.getDefaultChartOptions();
    return this.deepMerge(defaultOptions, chartOptions || {});
  }

  /**
   * Deep merge utility function
   */
  private deepMerge(target: any, source: any): any {
    const result = { ...target };
    
    for (const key in source) {
      if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
        result[key] = this.deepMerge(result[key] || {}, source[key]);
      } else {
        result[key] = source[key];
      }
    }
    
    return result;
  }

  /**
   * Format currency value
   */
  formatCurrency(value: number): string {
    const symbol = this.getCurrencySymbol();
    return `${symbol}${value.toLocaleString()}`;
  }

  /**
   * Format large numbers with K/L suffixes
   */
  formatLargeNumber(value: number): string {
    const symbol = this.getCurrencySymbol();
    
    if (value >= 10000000) { // 1 Crore
      return `${symbol}${(value / 10000000).toFixed(1)}Cr`;
    } else if (value >= 100000) { // 1 Lakh
      return `${symbol}${(value / 100000).toFixed(1)}L`;
    } else if (value >= 1000) { // 1 Thousand
      return `${symbol}${(value / 1000).toFixed(0)}K`;
    } else {
      return `${symbol}${value.toLocaleString()}`;
    }
  }

  /**
   * Get semantic colors for different data types
   */
  getSemanticColors(): { [key: string]: string } {
    return {
      positive: '#28a745',    // Green
      negative: '#dc3545',    // Red
      neutral: '#6c757d',     // Gray
      warning: '#ffc107',     // Yellow
      info: '#17a2b8',        // Cyan
      primary: '#007bff'      // Blue
    };
  }

  /**
   * Get color based on value type and context
   */
  getContextualColor(value: number, context: string = 'default'): string {
    const semanticColors = this.getSemanticColors();
    
    switch (context) {
      case 'growth':
      case 'profit':
        return value >= 0 ? semanticColors['positive'] : semanticColors['negative'];
      case 'loss':
      case 'spoilage':
        return semanticColors['negative'];
      case 'movement':
      case 'transfer':
        return semanticColors['info'];
      case 'warning':
        return semanticColors['warning'];
      default:
        return semanticColors['primary'];
    }
  }
}
