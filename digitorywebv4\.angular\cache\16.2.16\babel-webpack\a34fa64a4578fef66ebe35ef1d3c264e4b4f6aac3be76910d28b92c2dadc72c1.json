{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { NgChartsModule } from 'ng2-charts';\nimport { NgxMatSelectSearchModule } from 'ngx-mat-select-search';\nimport { FormControl, ReactiveFormsModule, FormsModule } from '@angular/forms';\nimport { Subject, takeUntil, debounceTime, distinctUntilChanged } from 'rxjs';\nlet SmartDashboardComponent = class SmartDashboardComponent {\n  constructor(smartDashboardService, authService, shareDataService, configService, chartRenderer, cdr) {\n    this.smartDashboardService = smartDashboardService;\n    this.authService = authService;\n    this.shareDataService = shareDataService;\n    this.configService = configService;\n    this.chartRenderer = chartRenderer;\n    this.cdr = cdr;\n    this.destroy$ = new Subject();\n    this.branches = [];\n    this.filteredBranches = [];\n    this.selectedLocations = [];\n    // Form controls\n    this.locationFilterCtrl = new FormControl('');\n    this.startDate = new FormControl();\n    this.endDate = new FormControl();\n    this.searchQuery = new FormControl('');\n    this.baseDateCtrl = new FormControl();\n    this.selectedDashboard = '';\n    // Dashboard data\n    this.summaryCards = [];\n    this.charts = [];\n    this.isLoading = false;\n    this.isConfigLoaded = false;\n    // Dynamic configuration data\n    this.dashboardTypes = [];\n    this.baseDateOptions = [];\n    this.user = this.authService.getCurrentUser();\n    this.initializeConfig();\n  }\n  initializeConfig() {\n    // Load dashboard configuration on component initialization\n    this.configService.loadConfig().subscribe({\n      next: response => {\n        if (response.status === 'success') {\n          this.configService.setConfig(response.data);\n          this.setupDynamicConfigurations(response.data);\n        } else {\n          this.setupDefaultConfigurations();\n        }\n        this.isConfigLoaded = true;\n        this.cdr.detectChanges();\n      },\n      error: () => {\n        this.setupDefaultConfigurations();\n        this.isConfigLoaded = true;\n        this.cdr.detectChanges();\n      }\n    });\n  }\n  setupDynamicConfigurations(config) {\n    // Set dashboard types\n    this.dashboardTypes = config.dashboard_types || [];\n    // Set base date options\n    this.baseDateOptions = config.base_date_options || [];\n    // Set default values from UI config\n    const uiConfig = config.ui_config || {};\n    // Set default form values\n    this.selectedDashboard = uiConfig.default_dashboard_type || 'purchase';\n    this.baseDateCtrl.setValue(uiConfig.default_base_date || 'deliveryDate');\n    // Set date range based on dashboard type\n    this.setDefaultDateRange();\n    // Configuration is set - user can now click Search to load data\n    // No automatic data loading\n  }\n\n  setupDefaultConfigurations() {\n    // Fallback configurations if backend fails\n    this.dashboardTypes = [{\n      value: 'purchase',\n      label: 'Purchase Dashboard'\n    }, {\n      value: 'inventory',\n      label: 'Inventory Dashboard'\n    }];\n    this.baseDateOptions = [{\n      value: 'deliveryDate',\n      label: 'Delivery Date'\n    }, {\n      value: 'orderDate',\n      label: 'Order Date'\n    }, {\n      value: 'createdDate',\n      label: 'Created Date'\n    }];\n    this.selectedDashboard = 'inventory';\n    this.baseDateCtrl.setValue('deliveryDate');\n    // Set date range based on dashboard type\n    this.setDefaultDateRange();\n    // Fallback configuration is set - user can now click Search to load data\n    // No automatic data loading\n  }\n\n  ngOnInit() {\n    this.initializeFilters();\n    this.loadBranches();\n    // Don't load dashboard data immediately - wait for config to load\n    // Dashboard data will be loaded after config is ready\n  }\n\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  initializeFilters() {\n    // Location filter\n    this.locationFilterCtrl.valueChanges.pipe(debounceTime(200), distinctUntilChanged(), takeUntil(this.destroy$)).subscribe(value => {\n      this.filterBranches(value || '');\n    });\n  }\n  loadBranches() {\n    this.shareDataService.selectedBranchesSource.pipe(takeUntil(this.destroy$)).subscribe(data => {\n      this.branches = data || [];\n      this.filteredBranches = [...this.branches];\n      // Select all branches by default\n      this.selectedLocations = this.branches.map(branch => branch.restaurantIdOld);\n    });\n  }\n  filterBranches(searchTerm) {\n    if (!searchTerm) {\n      this.filteredBranches = [...this.branches];\n    } else {\n      const normalizedSearchTerm = searchTerm.toLowerCase().replace(/\\s/g, '');\n      this.filteredBranches = this.branches.filter(branch => branch.branchName.toLowerCase().replace(/\\s/g, '').includes(normalizedSearchTerm));\n    }\n  }\n  loadDashboardData() {\n    if (!this.startDate.value || !this.endDate.value || !this.selectedDashboard) {\n      return;\n    }\n    this.isLoading = true;\n    const filters = {\n      locations: this.selectedLocations || [],\n      startDate: this.formatDate(this.startDate.value),\n      endDate: this.formatDate(this.endDate.value),\n      baseDate: this.baseDateCtrl.value || 'deliveryDate'\n    };\n    const request = {\n      tenant_id: this.user.tenantId,\n      filters: filters,\n      user_query: '',\n      use_default_charts: true,\n      dashboard_type: this.selectedDashboard\n    };\n    this.smartDashboardService.getSmartDashboardData(request).pipe(takeUntil(this.destroy$)).subscribe({\n      next: response => {\n        if (response.status === 'success') {\n          this.processDashboardData(response.data);\n        } else {\n          this.clearDashboardData();\n        }\n        this.isLoading = false;\n        this.cdr.detectChanges();\n      },\n      error: () => {\n        this.clearDashboardData();\n        this.isLoading = false;\n        this.cdr.detectChanges();\n      }\n    });\n  }\n  clearDashboardData() {\n    this.summaryCards = [];\n    this.charts = [];\n  }\n  processDashboardData(data) {\n    // Process summary cards using config service\n    this.summaryCards = data.summary_items?.map(item => ({\n      icon: item.icon || this.configService.getSummaryCardIcon(item.data_type),\n      value: item.value,\n      label: item.label,\n      color: this.configService.getSummaryCardColor(item.data_type),\n      data_type: item.data_type\n    })) || [];\n    // Process charts using chart renderer service\n    this.charts = data.charts?.map(chart => this.chartRenderer.processChart(chart)) || [];\n  }\n  formatDate(date) {\n    // Fix the date offset issue by using local date components\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const day = String(date.getDate()).padStart(2, '0');\n    return `${year}-${month}-${day}`;\n  }\n  setDefaultDateRange() {\n    const today = new Date();\n    if (this.selectedDashboard === 'inventory') {\n      // For inventory dashboard: current month start to current date\n      const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);\n      this.startDate.setValue(startOfMonth);\n      this.endDate.setValue(today);\n    } else {\n      // For purchase dashboard: last 30 days (existing behavior)\n      const thirtyDaysAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);\n      this.startDate.setValue(thirtyDaysAgo);\n      this.endDate.setValue(today);\n    }\n  }\n  onLocationChange() {\n    // No automatic API call - user must click Search button\n  }\n  onDateChange() {\n    // No automatic API call - user must click Search button\n  }\n  onDashboardChange() {\n    // Update date range when dashboard type changes\n    this.setDefaultDateRange();\n    // No automatic API call - user must click Search button\n  }\n\n  searchDashboard() {\n    // This method is called when Search button is clicked\n    this.loadDashboardData();\n  }\n  resetFilters() {\n    // Reset all filters to default values\n    this.selectedLocations = this.branches.map(branch => branch.restaurantIdOld);\n    this.setDefaultDateRange();\n    this.baseDateCtrl.setValue('deliveryDate');\n    this.searchQuery.setValue('');\n    this.locationFilterCtrl.setValue('');\n    this.filteredBranches = [...this.branches];\n    // Load dashboard data with reset filters\n    this.loadDashboardData();\n  }\n  onSearchQuery() {\n    const query = this.searchQuery.value?.trim();\n    if (query) {\n      this.loadDashboardDataWithQuery(query);\n    } else {\n      this.loadDashboardData();\n    }\n  }\n  loadDashboardDataWithQuery(query) {\n    if (!this.startDate.value || !this.endDate.value || !this.selectedDashboard) {\n      return;\n    }\n    this.isLoading = true;\n    const filters = {\n      locations: this.selectedLocations || [],\n      startDate: this.formatDate(this.startDate.value),\n      endDate: this.formatDate(this.endDate.value),\n      baseDate: this.baseDateCtrl.value || 'deliveryDate'\n    };\n    const request = {\n      tenant_id: this.user.tenantId,\n      filters: filters,\n      user_query: query,\n      use_default_charts: false,\n      dashboard_type: this.selectedDashboard\n    };\n    this.smartDashboardService.getSmartDashboardData(request).pipe(takeUntil(this.destroy$)).subscribe({\n      next: response => {\n        if (response.status === 'success') {\n          this.processDashboardData(response.data);\n        } else {\n          this.clearDashboardData();\n        }\n        this.isLoading = false;\n        this.cdr.detectChanges();\n      },\n      error: () => {\n        this.clearDashboardData();\n        this.isLoading = false;\n        this.cdr.detectChanges();\n      }\n    });\n  }\n  // Dynamic chart methods using services\n  getChartData(chart) {\n    return chart.data;\n  }\n  getChartType(chart) {\n    return this.chartRenderer.getChartType(chart.type);\n  }\n  getChartOptions(chart) {\n    return chart.options || this.configService.getDefaultChartOptions();\n  }\n  getChartCssClass(chart) {\n    return this.chartRenderer.getChartCssClass(chart);\n  }\n};\nSmartDashboardComponent = __decorate([Component({\n  selector: 'app-smart-dashboard',\n  standalone: true,\n  imports: [CommonModule, MatCardModule, MatButtonModule, MatIconModule, MatSelectModule, MatFormFieldModule, MatInputModule, MatDatepickerModule, MatNativeDateModule, MatDividerModule, MatProgressSpinnerModule, MatCheckboxModule, NgChartsModule, NgxMatSelectSearchModule, ReactiveFormsModule, FormsModule],\n  templateUrl: './smart-dashboard.component.html',\n  styleUrls: ['./smart-dashboard.component.scss']\n})], SmartDashboardComponent);\nexport { SmartDashboardComponent };", "map": {"version": 3, "names": ["Component", "CommonModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatSelectModule", "MatFormFieldModule", "MatInputModule", "MatDatepickerModule", "MatNativeDateModule", "MatDividerModule", "MatProgressSpinnerModule", "NgChartsModule", "NgxMatSelectSearchModule", "FormControl", "ReactiveFormsModule", "FormsModule", "Subject", "takeUntil", "debounceTime", "distinctUntilChanged", "SmartDashboardComponent", "constructor", "smartDashboardService", "authService", "shareDataService", "configService", "<PERSON><PERSON><PERSON><PERSON>", "cdr", "destroy$", "branches", "filteredBranches", "selectedLocations", "locationFilterCtrl", "startDate", "endDate", "searchQuery", "baseDateCtrl", "selectedDashboard", "summaryCards", "charts", "isLoading", "isConfigLoaded", "dashboardTypes", "baseDateOptions", "user", "getCurrentUser", "initializeConfig", "loadConfig", "subscribe", "next", "response", "status", "setConfig", "data", "setupDynamicConfigurations", "setupDefaultConfigurations", "detectChanges", "error", "config", "dashboard_types", "base_date_options", "uiConfig", "ui_config", "default_dashboard_type", "setValue", "default_base_date", "setDefaultDateRange", "value", "label", "ngOnInit", "initializeFilters", "loadBranches", "ngOnDestroy", "complete", "valueChanges", "pipe", "filterBranches", "selectedBranchesSource", "map", "branch", "restaurantIdOld", "searchTerm", "normalizedSearchTerm", "toLowerCase", "replace", "filter", "branchName", "includes", "loadDashboardData", "filters", "locations", "formatDate", "baseDate", "request", "tenant_id", "tenantId", "user_query", "use_default_charts", "dashboard_type", "getSmartDashboardData", "processDashboardData", "clearDashboardData", "summary_items", "item", "icon", "getSummaryCardIcon", "data_type", "color", "getSummaryCardColor", "chart", "processChart", "date", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "today", "Date", "startOfMonth", "thirtyDaysAgo", "getTime", "onLocationChange", "onDateChange", "onDashboardChange", "searchDashboard", "resetFilters", "onSearchQuery", "query", "trim", "loadDashboardDataWithQuery", "getChartData", "getChartType", "type", "getChartOptions", "options", "getDefaultChartOptions", "getChartCssClass", "__decorate", "selector", "standalone", "imports", "MatCheckboxModule", "templateUrl", "styleUrls"], "sources": ["C:\\Users\\<USER>\\Desktop\\digii\\digitorywebv4\\src\\app\\pages\\smart-dashboard\\smart-dashboard.component.ts"], "sourcesContent": ["import { <PERSON>mpo<PERSON>, On<PERSON>ni<PERSON>, <PERSON><PERSON><PERSON>roy, ChangeDetectorRef } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { NgChartsModule } from 'ng2-charts';\nimport { NgxMatSelectSearchModule } from 'ngx-mat-select-search';\nimport { FormControl, ReactiveFormsModule, FormsModule } from '@angular/forms';\nimport { Subject, takeUntil, debounceTime, distinctUntilChanged } from 'rxjs';\nimport { ChartConfiguration, ChartData, ChartType } from 'chart.js';\n\nimport { SmartDashboardService } from '../../services/smart-dashboard.service';\nimport { AuthService } from '../../services/auth.service';\nimport { ShareDataService } from '../../services/share-data.service';\nimport { DashboardConfigService, DashboardType, BaseDateOption } from '../../services/dashboard-config.service';\nimport { ChartRendererService, ChartModel } from '../../services/chart-renderer.service';\n\ninterface SummaryCard {\n  icon: string;\n  value: string;\n  label: string;\n  color: string;\n  data_type?: string;\n}\n\n@Component({\n  selector: 'app-smart-dashboard',\n  standalone: true,\n  imports: [\n    CommonModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatSelectModule,\n    MatFormFieldModule,\n    MatInputModule,\n    MatDatepickerModule,\n    MatNativeDateModule,\n    MatDividerModule,\n    MatProgressSpinnerModule,\n    MatCheckboxModule,\n    NgChartsModule,\n    NgxMatSelectSearchModule,\n    ReactiveFormsModule,\n    FormsModule\n  ],\n  templateUrl: './smart-dashboard.component.html',\n  styleUrls: ['./smart-dashboard.component.scss']\n})\nexport class SmartDashboardComponent implements OnInit, OnDestroy {\n  private destroy$ = new Subject<void>();\n  \n  // User and branch data\n  user: any;\n  branches: any[] = [];\n  filteredBranches: any[] = [];\n  selectedLocations: string[] = [];\n  \n  // Form controls\n  locationFilterCtrl = new FormControl('');\n  startDate = new FormControl();\n  endDate = new FormControl();\n  searchQuery = new FormControl('');\n  baseDateCtrl = new FormControl();\n  selectedDashboard = '';\n\n  // Dashboard data\n  summaryCards: SummaryCard[] = [];\n  charts: ChartModel[] = [];\n  isLoading = false;\n  isConfigLoaded = false;\n\n  // Dynamic configuration data\n  dashboardTypes: DashboardType[] = [];\n  baseDateOptions: BaseDateOption[] = [];\n\n\n\n\n\n  constructor(\n    private smartDashboardService: SmartDashboardService,\n    private authService: AuthService,\n    private shareDataService: ShareDataService,\n    private configService: DashboardConfigService,\n    private chartRenderer: ChartRendererService,\n    private cdr: ChangeDetectorRef\n  ) {\n    this.user = this.authService.getCurrentUser();\n    this.initializeConfig();\n  }\n\n  private initializeConfig(): void {\n    // Load dashboard configuration on component initialization\n    this.configService.loadConfig().subscribe({\n      next: (response) => {\n        if (response.status === 'success') {\n          this.configService.setConfig(response.data);\n          this.setupDynamicConfigurations(response.data);\n        } else {\n          this.setupDefaultConfigurations();\n        }\n        this.isConfigLoaded = true;\n        this.cdr.detectChanges();\n      },\n      error: () => {\n        this.setupDefaultConfigurations();\n        this.isConfigLoaded = true;\n        this.cdr.detectChanges();\n      }\n    });\n  }\n\n  private setupDynamicConfigurations(config: any): void {\n    // Set dashboard types\n    this.dashboardTypes = config.dashboard_types || [];\n\n    // Set base date options\n    this.baseDateOptions = config.base_date_options || [];\n\n    // Set default values from UI config\n    const uiConfig = config.ui_config || {};\n\n    // Set default form values\n    this.selectedDashboard = uiConfig.default_dashboard_type || 'purchase';\n    this.baseDateCtrl.setValue(uiConfig.default_base_date || 'deliveryDate');\n\n    // Set date range based on dashboard type\n    this.setDefaultDateRange();\n\n    // Configuration is set - user can now click Search to load data\n    // No automatic data loading\n  }\n\n  private setupDefaultConfigurations(): void {\n    // Fallback configurations if backend fails\n    this.dashboardTypes = [\n      { value: 'purchase', label: 'Purchase Dashboard' },\n      { value: 'inventory', label: 'Inventory Dashboard' },\n    ];\n    this.baseDateOptions = [\n      { value: 'deliveryDate', label: 'Delivery Date' },\n      { value: 'orderDate', label: 'Order Date' },\n      { value: 'createdDate', label: 'Created Date' }\n    ];\n    this.selectedDashboard = 'inventory';\n    this.baseDateCtrl.setValue('deliveryDate');\n\n    // Set date range based on dashboard type\n    this.setDefaultDateRange();\n\n    // Fallback configuration is set - user can now click Search to load data\n    // No automatic data loading\n  }\n\n  ngOnInit(): void {\n    this.initializeFilters();\n    this.loadBranches();\n    // Don't load dashboard data immediately - wait for config to load\n    // Dashboard data will be loaded after config is ready\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  private initializeFilters(): void {\n    // Location filter\n    this.locationFilterCtrl.valueChanges\n      .pipe(\n        debounceTime(200),\n        distinctUntilChanged(),\n        takeUntil(this.destroy$)\n      )\n      .subscribe((value: string | null) => {\n        this.filterBranches(value || '');\n      });\n  }\n\n  private loadBranches(): void {\n    this.shareDataService.selectedBranchesSource\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(data => {\n        this.branches = data || [];\n        this.filteredBranches = [...this.branches];\n\n        // Select all branches by default\n        this.selectedLocations = this.branches.map(branch => branch.restaurantIdOld);\n      });\n  }\n\n  private filterBranches(searchTerm: string): void {\n    if (!searchTerm) {\n      this.filteredBranches = [...this.branches];\n    } else {\n      const normalizedSearchTerm = searchTerm.toLowerCase().replace(/\\s/g, '');\n      this.filteredBranches = this.branches.filter(branch =>\n        branch.branchName.toLowerCase().replace(/\\s/g, '').includes(normalizedSearchTerm)\n      );\n    }\n  }\n\n  loadDashboardData(): void {\n    if (!this.startDate.value || !this.endDate.value || !this.selectedDashboard) {\n      return;\n    }\n\n    this.isLoading = true;\n\n    const filters = {\n      locations: this.selectedLocations || [],\n      startDate: this.formatDate(this.startDate.value),\n      endDate: this.formatDate(this.endDate.value),\n      baseDate: this.baseDateCtrl.value || 'deliveryDate'\n    };\n\n    const request = {\n      tenant_id: this.user.tenantId,\n      filters: filters,\n      user_query: '',\n      use_default_charts: true,\n      dashboard_type: this.selectedDashboard\n    };\n\n    this.smartDashboardService.getSmartDashboardData(request)\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: (response) => {\n          if (response.status === 'success') {\n            this.processDashboardData(response.data);\n          } else {\n            this.clearDashboardData();\n          }\n          this.isLoading = false;\n          this.cdr.detectChanges();\n        },\n        error: () => {\n          this.clearDashboardData();\n          this.isLoading = false;\n          this.cdr.detectChanges();\n        }\n      });\n  }\n\n  private clearDashboardData(): void {\n    this.summaryCards = [];\n    this.charts = [];\n  }\n\n  private processDashboardData(data: any): void {\n    // Process summary cards using config service\n    this.summaryCards = data.summary_items?.map((item: any) => ({\n      icon: item.icon || this.configService.getSummaryCardIcon(item.data_type),\n      value: item.value,\n      label: item.label,\n      color: this.configService.getSummaryCardColor(item.data_type),\n      data_type: item.data_type\n    })) || [];\n\n    // Process charts using chart renderer service\n    this.charts = data.charts?.map((chart: any) =>\n      this.chartRenderer.processChart(chart)\n    ) || [];\n  }\n\n  private formatDate(date: Date): string {\n    // Fix the date offset issue by using local date components\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const day = String(date.getDate()).padStart(2, '0');\n    return `${year}-${month}-${day}`;\n  }\n\n  private setDefaultDateRange(): void {\n    const today = new Date();\n\n    if (this.selectedDashboard === 'inventory') {\n      // For inventory dashboard: current month start to current date\n      const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);\n      this.startDate.setValue(startOfMonth);\n      this.endDate.setValue(today);\n    } else {\n      // For purchase dashboard: last 30 days (existing behavior)\n      const thirtyDaysAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);\n      this.startDate.setValue(thirtyDaysAgo);\n      this.endDate.setValue(today);\n    }\n  }\n\n  onLocationChange(): void {\n    // No automatic API call - user must click Search button\n  }\n\n  onDateChange(): void {\n    // No automatic API call - user must click Search button\n  }\n\n  onDashboardChange(): void {\n    // Update date range when dashboard type changes\n    this.setDefaultDateRange();\n    // No automatic API call - user must click Search button\n  }\n\n  searchDashboard(): void {\n    // This method is called when Search button is clicked\n    this.loadDashboardData();\n  }\n\n  resetFilters(): void {\n    // Reset all filters to default values\n    this.selectedLocations = this.branches.map(branch => branch.restaurantIdOld);\n    this.setDefaultDateRange();\n    this.baseDateCtrl.setValue('deliveryDate');\n    this.searchQuery.setValue('');\n    this.locationFilterCtrl.setValue('');\n    this.filteredBranches = [...this.branches];\n\n    // Load dashboard data with reset filters\n    this.loadDashboardData();\n  }\n\n  onSearchQuery(): void {\n    const query = this.searchQuery.value?.trim();\n    if (query) {\n      this.loadDashboardDataWithQuery(query);\n    } else {\n      this.loadDashboardData();\n    }\n  }\n\n  private loadDashboardDataWithQuery(query: string): void {\n    if (!this.startDate.value || !this.endDate.value || !this.selectedDashboard) {\n      return;\n    }\n\n    this.isLoading = true;\n\n    const filters = {\n      locations: this.selectedLocations || [],\n      startDate: this.formatDate(this.startDate.value),\n      endDate: this.formatDate(this.endDate.value),\n      baseDate: this.baseDateCtrl.value || 'deliveryDate'\n    };\n\n    const request = {\n      tenant_id: this.user.tenantId,\n      filters: filters,\n      user_query: query,\n      use_default_charts: false,\n      dashboard_type: this.selectedDashboard\n    };\n\n    this.smartDashboardService.getSmartDashboardData(request)\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: (response) => {\n          if (response.status === 'success') {\n            this.processDashboardData(response.data);\n          } else {\n            this.clearDashboardData();\n          }\n          this.isLoading = false;\n          this.cdr.detectChanges();\n        },\n        error: () => {\n          this.clearDashboardData();\n          this.isLoading = false;\n          this.cdr.detectChanges();\n        }\n      });\n  }\n\n  // Dynamic chart methods using services\n  getChartData(chart: ChartModel): ChartData {\n    return chart.data;\n  }\n\n  getChartType(chart: ChartModel): ChartType {\n    return this.chartRenderer.getChartType(chart.type);\n  }\n\n  getChartOptions(chart: ChartModel): ChartConfiguration['options'] {\n    return chart.options || this.configService.getDefaultChartOptions();\n  }\n\n  getChartCssClass(chart: ChartModel): string {\n    return this.chartRenderer.getChartCssClass(chart);\n  }\n}\n"], "mappings": ";AAAA,SAASA,SAAS,QAA8C,eAAe;AAC/E,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,mBAAmB,QAAQ,wBAAwB;AAC5D,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,cAAc,QAAQ,YAAY;AAC3C,SAASC,wBAAwB,QAAQ,uBAAuB;AAChE,SAASC,WAAW,EAAEC,mBAAmB,EAAEC,WAAW,QAAQ,gBAAgB;AAC9E,SAASC,OAAO,EAAEC,SAAS,EAAEC,YAAY,EAAEC,oBAAoB,QAAQ,MAAM;AAyC7E,IAAaC,uBAAuB,GAApC,MAAaA,uBAAuB;EA+BlCC,YACUC,qBAA4C,EAC5CC,WAAwB,EACxBC,gBAAkC,EAClCC,aAAqC,EACrCC,aAAmC,EACnCC,GAAsB;IALtB,KAAAL,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,GAAG,GAAHA,GAAG;IApCL,KAAAC,QAAQ,GAAG,IAAIZ,OAAO,EAAQ;IAItC,KAAAa,QAAQ,GAAU,EAAE;IACpB,KAAAC,gBAAgB,GAAU,EAAE;IAC5B,KAAAC,iBAAiB,GAAa,EAAE;IAEhC;IACA,KAAAC,kBAAkB,GAAG,IAAInB,WAAW,CAAC,EAAE,CAAC;IACxC,KAAAoB,SAAS,GAAG,IAAIpB,WAAW,EAAE;IAC7B,KAAAqB,OAAO,GAAG,IAAIrB,WAAW,EAAE;IAC3B,KAAAsB,WAAW,GAAG,IAAItB,WAAW,CAAC,EAAE,CAAC;IACjC,KAAAuB,YAAY,GAAG,IAAIvB,WAAW,EAAE;IAChC,KAAAwB,iBAAiB,GAAG,EAAE;IAEtB;IACA,KAAAC,YAAY,GAAkB,EAAE;IAChC,KAAAC,MAAM,GAAiB,EAAE;IACzB,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,cAAc,GAAG,KAAK;IAEtB;IACA,KAAAC,cAAc,GAAoB,EAAE;IACpC,KAAAC,eAAe,GAAqB,EAAE;IAcpC,IAAI,CAACC,IAAI,GAAG,IAAI,CAACrB,WAAW,CAACsB,cAAc,EAAE;IAC7C,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEQA,gBAAgBA,CAAA;IACtB;IACA,IAAI,CAACrB,aAAa,CAACsB,UAAU,EAAE,CAACC,SAAS,CAAC;MACxCC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,MAAM,KAAK,SAAS,EAAE;UACjC,IAAI,CAAC1B,aAAa,CAAC2B,SAAS,CAACF,QAAQ,CAACG,IAAI,CAAC;UAC3C,IAAI,CAACC,0BAA0B,CAACJ,QAAQ,CAACG,IAAI,CAAC;SAC/C,MAAM;UACL,IAAI,CAACE,0BAA0B,EAAE;;QAEnC,IAAI,CAACd,cAAc,GAAG,IAAI;QAC1B,IAAI,CAACd,GAAG,CAAC6B,aAAa,EAAE;MAC1B,CAAC;MACDC,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAACF,0BAA0B,EAAE;QACjC,IAAI,CAACd,cAAc,GAAG,IAAI;QAC1B,IAAI,CAACd,GAAG,CAAC6B,aAAa,EAAE;MAC1B;KACD,CAAC;EACJ;EAEQF,0BAA0BA,CAACI,MAAW;IAC5C;IACA,IAAI,CAAChB,cAAc,GAAGgB,MAAM,CAACC,eAAe,IAAI,EAAE;IAElD;IACA,IAAI,CAAChB,eAAe,GAAGe,MAAM,CAACE,iBAAiB,IAAI,EAAE;IAErD;IACA,MAAMC,QAAQ,GAAGH,MAAM,CAACI,SAAS,IAAI,EAAE;IAEvC;IACA,IAAI,CAACzB,iBAAiB,GAAGwB,QAAQ,CAACE,sBAAsB,IAAI,UAAU;IACtE,IAAI,CAAC3B,YAAY,CAAC4B,QAAQ,CAACH,QAAQ,CAACI,iBAAiB,IAAI,cAAc,CAAC;IAExE;IACA,IAAI,CAACC,mBAAmB,EAAE;IAE1B;IACA;EACF;;EAEQX,0BAA0BA,CAAA;IAChC;IACA,IAAI,CAACb,cAAc,GAAG,CACpB;MAAEyB,KAAK,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAoB,CAAE,EAClD;MAAED,KAAK,EAAE,WAAW;MAAEC,KAAK,EAAE;IAAqB,CAAE,CACrD;IACD,IAAI,CAACzB,eAAe,GAAG,CACrB;MAAEwB,KAAK,EAAE,cAAc;MAAEC,KAAK,EAAE;IAAe,CAAE,EACjD;MAAED,KAAK,EAAE,WAAW;MAAEC,KAAK,EAAE;IAAY,CAAE,EAC3C;MAAED,KAAK,EAAE,aAAa;MAAEC,KAAK,EAAE;IAAc,CAAE,CAChD;IACD,IAAI,CAAC/B,iBAAiB,GAAG,WAAW;IACpC,IAAI,CAACD,YAAY,CAAC4B,QAAQ,CAAC,cAAc,CAAC;IAE1C;IACA,IAAI,CAACE,mBAAmB,EAAE;IAE1B;IACA;EACF;;EAEAG,QAAQA,CAAA;IACN,IAAI,CAACC,iBAAiB,EAAE;IACxB,IAAI,CAACC,YAAY,EAAE;IACnB;IACA;EACF;;EAEAC,WAAWA,CAAA;IACT,IAAI,CAAC5C,QAAQ,CAACqB,IAAI,EAAE;IACpB,IAAI,CAACrB,QAAQ,CAAC6C,QAAQ,EAAE;EAC1B;EAEQH,iBAAiBA,CAAA;IACvB;IACA,IAAI,CAACtC,kBAAkB,CAAC0C,YAAY,CACjCC,IAAI,CACHzD,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBF,SAAS,CAAC,IAAI,CAACW,QAAQ,CAAC,CACzB,CACAoB,SAAS,CAAEmB,KAAoB,IAAI;MAClC,IAAI,CAACS,cAAc,CAACT,KAAK,IAAI,EAAE,CAAC;IAClC,CAAC,CAAC;EACN;EAEQI,YAAYA,CAAA;IAClB,IAAI,CAAC/C,gBAAgB,CAACqD,sBAAsB,CACzCF,IAAI,CAAC1D,SAAS,CAAC,IAAI,CAACW,QAAQ,CAAC,CAAC,CAC9BoB,SAAS,CAACK,IAAI,IAAG;MAChB,IAAI,CAACxB,QAAQ,GAAGwB,IAAI,IAAI,EAAE;MAC1B,IAAI,CAACvB,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAACD,QAAQ,CAAC;MAE1C;MACA,IAAI,CAACE,iBAAiB,GAAG,IAAI,CAACF,QAAQ,CAACiD,GAAG,CAACC,MAAM,IAAIA,MAAM,CAACC,eAAe,CAAC;IAC9E,CAAC,CAAC;EACN;EAEQJ,cAAcA,CAACK,UAAkB;IACvC,IAAI,CAACA,UAAU,EAAE;MACf,IAAI,CAACnD,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAACD,QAAQ,CAAC;KAC3C,MAAM;MACL,MAAMqD,oBAAoB,GAAGD,UAAU,CAACE,WAAW,EAAE,CAACC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;MACxE,IAAI,CAACtD,gBAAgB,GAAG,IAAI,CAACD,QAAQ,CAACwD,MAAM,CAACN,MAAM,IACjDA,MAAM,CAACO,UAAU,CAACH,WAAW,EAAE,CAACC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAACG,QAAQ,CAACL,oBAAoB,CAAC,CAClF;;EAEL;EAEAM,iBAAiBA,CAAA;IACf,IAAI,CAAC,IAAI,CAACvD,SAAS,CAACkC,KAAK,IAAI,CAAC,IAAI,CAACjC,OAAO,CAACiC,KAAK,IAAI,CAAC,IAAI,CAAC9B,iBAAiB,EAAE;MAC3E;;IAGF,IAAI,CAACG,SAAS,GAAG,IAAI;IAErB,MAAMiD,OAAO,GAAG;MACdC,SAAS,EAAE,IAAI,CAAC3D,iBAAiB,IAAI,EAAE;MACvCE,SAAS,EAAE,IAAI,CAAC0D,UAAU,CAAC,IAAI,CAAC1D,SAAS,CAACkC,KAAK,CAAC;MAChDjC,OAAO,EAAE,IAAI,CAACyD,UAAU,CAAC,IAAI,CAACzD,OAAO,CAACiC,KAAK,CAAC;MAC5CyB,QAAQ,EAAE,IAAI,CAACxD,YAAY,CAAC+B,KAAK,IAAI;KACtC;IAED,MAAM0B,OAAO,GAAG;MACdC,SAAS,EAAE,IAAI,CAAClD,IAAI,CAACmD,QAAQ;MAC7BN,OAAO,EAAEA,OAAO;MAChBO,UAAU,EAAE,EAAE;MACdC,kBAAkB,EAAE,IAAI;MACxBC,cAAc,EAAE,IAAI,CAAC7D;KACtB;IAED,IAAI,CAACf,qBAAqB,CAAC6E,qBAAqB,CAACN,OAAO,CAAC,CACtDlB,IAAI,CAAC1D,SAAS,CAAC,IAAI,CAACW,QAAQ,CAAC,CAAC,CAC9BoB,SAAS,CAAC;MACTC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,MAAM,KAAK,SAAS,EAAE;UACjC,IAAI,CAACiD,oBAAoB,CAAClD,QAAQ,CAACG,IAAI,CAAC;SACzC,MAAM;UACL,IAAI,CAACgD,kBAAkB,EAAE;;QAE3B,IAAI,CAAC7D,SAAS,GAAG,KAAK;QACtB,IAAI,CAACb,GAAG,CAAC6B,aAAa,EAAE;MAC1B,CAAC;MACDC,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAAC4C,kBAAkB,EAAE;QACzB,IAAI,CAAC7D,SAAS,GAAG,KAAK;QACtB,IAAI,CAACb,GAAG,CAAC6B,aAAa,EAAE;MAC1B;KACD,CAAC;EACN;EAEQ6C,kBAAkBA,CAAA;IACxB,IAAI,CAAC/D,YAAY,GAAG,EAAE;IACtB,IAAI,CAACC,MAAM,GAAG,EAAE;EAClB;EAEQ6D,oBAAoBA,CAAC/C,IAAS;IACpC;IACA,IAAI,CAACf,YAAY,GAAGe,IAAI,CAACiD,aAAa,EAAExB,GAAG,CAAEyB,IAAS,KAAM;MAC1DC,IAAI,EAAED,IAAI,CAACC,IAAI,IAAI,IAAI,CAAC/E,aAAa,CAACgF,kBAAkB,CAACF,IAAI,CAACG,SAAS,CAAC;MACxEvC,KAAK,EAAEoC,IAAI,CAACpC,KAAK;MACjBC,KAAK,EAAEmC,IAAI,CAACnC,KAAK;MACjBuC,KAAK,EAAE,IAAI,CAAClF,aAAa,CAACmF,mBAAmB,CAACL,IAAI,CAACG,SAAS,CAAC;MAC7DA,SAAS,EAAEH,IAAI,CAACG;KACjB,CAAC,CAAC,IAAI,EAAE;IAET;IACA,IAAI,CAACnE,MAAM,GAAGc,IAAI,CAACd,MAAM,EAAEuC,GAAG,CAAE+B,KAAU,IACxC,IAAI,CAACnF,aAAa,CAACoF,YAAY,CAACD,KAAK,CAAC,CACvC,IAAI,EAAE;EACT;EAEQlB,UAAUA,CAACoB,IAAU;IAC3B;IACA,MAAMC,IAAI,GAAGD,IAAI,CAACE,WAAW,EAAE;IAC/B,MAAMC,KAAK,GAAGC,MAAM,CAACJ,IAAI,CAACK,QAAQ,EAAE,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC1D,MAAMC,GAAG,GAAGH,MAAM,CAACJ,IAAI,CAACQ,OAAO,EAAE,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACnD,OAAO,GAAGL,IAAI,IAAIE,KAAK,IAAII,GAAG,EAAE;EAClC;EAEQpD,mBAAmBA,CAAA;IACzB,MAAMsD,KAAK,GAAG,IAAIC,IAAI,EAAE;IAExB,IAAI,IAAI,CAACpF,iBAAiB,KAAK,WAAW,EAAE;MAC1C;MACA,MAAMqF,YAAY,GAAG,IAAID,IAAI,CAACD,KAAK,CAACP,WAAW,EAAE,EAAEO,KAAK,CAACJ,QAAQ,EAAE,EAAE,CAAC,CAAC;MACvE,IAAI,CAACnF,SAAS,CAAC+B,QAAQ,CAAC0D,YAAY,CAAC;MACrC,IAAI,CAACxF,OAAO,CAAC8B,QAAQ,CAACwD,KAAK,CAAC;KAC7B,MAAM;MACL;MACA,MAAMG,aAAa,GAAG,IAAIF,IAAI,CAACD,KAAK,CAACI,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;MAC1E,IAAI,CAAC3F,SAAS,CAAC+B,QAAQ,CAAC2D,aAAa,CAAC;MACtC,IAAI,CAACzF,OAAO,CAAC8B,QAAQ,CAACwD,KAAK,CAAC;;EAEhC;EAEAK,gBAAgBA,CAAA;IACd;EAAA;EAGFC,YAAYA,CAAA;IACV;EAAA;EAGFC,iBAAiBA,CAAA;IACf;IACA,IAAI,CAAC7D,mBAAmB,EAAE;IAC1B;EACF;;EAEA8D,eAAeA,CAAA;IACb;IACA,IAAI,CAACxC,iBAAiB,EAAE;EAC1B;EAEAyC,YAAYA,CAAA;IACV;IACA,IAAI,CAAClG,iBAAiB,GAAG,IAAI,CAACF,QAAQ,CAACiD,GAAG,CAACC,MAAM,IAAIA,MAAM,CAACC,eAAe,CAAC;IAC5E,IAAI,CAACd,mBAAmB,EAAE;IAC1B,IAAI,CAAC9B,YAAY,CAAC4B,QAAQ,CAAC,cAAc,CAAC;IAC1C,IAAI,CAAC7B,WAAW,CAAC6B,QAAQ,CAAC,EAAE,CAAC;IAC7B,IAAI,CAAChC,kBAAkB,CAACgC,QAAQ,CAAC,EAAE,CAAC;IACpC,IAAI,CAAClC,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAACD,QAAQ,CAAC;IAE1C;IACA,IAAI,CAAC2D,iBAAiB,EAAE;EAC1B;EAEA0C,aAAaA,CAAA;IACX,MAAMC,KAAK,GAAG,IAAI,CAAChG,WAAW,CAACgC,KAAK,EAAEiE,IAAI,EAAE;IAC5C,IAAID,KAAK,EAAE;MACT,IAAI,CAACE,0BAA0B,CAACF,KAAK,CAAC;KACvC,MAAM;MACL,IAAI,CAAC3C,iBAAiB,EAAE;;EAE5B;EAEQ6C,0BAA0BA,CAACF,KAAa;IAC9C,IAAI,CAAC,IAAI,CAAClG,SAAS,CAACkC,KAAK,IAAI,CAAC,IAAI,CAACjC,OAAO,CAACiC,KAAK,IAAI,CAAC,IAAI,CAAC9B,iBAAiB,EAAE;MAC3E;;IAGF,IAAI,CAACG,SAAS,GAAG,IAAI;IAErB,MAAMiD,OAAO,GAAG;MACdC,SAAS,EAAE,IAAI,CAAC3D,iBAAiB,IAAI,EAAE;MACvCE,SAAS,EAAE,IAAI,CAAC0D,UAAU,CAAC,IAAI,CAAC1D,SAAS,CAACkC,KAAK,CAAC;MAChDjC,OAAO,EAAE,IAAI,CAACyD,UAAU,CAAC,IAAI,CAACzD,OAAO,CAACiC,KAAK,CAAC;MAC5CyB,QAAQ,EAAE,IAAI,CAACxD,YAAY,CAAC+B,KAAK,IAAI;KACtC;IAED,MAAM0B,OAAO,GAAG;MACdC,SAAS,EAAE,IAAI,CAAClD,IAAI,CAACmD,QAAQ;MAC7BN,OAAO,EAAEA,OAAO;MAChBO,UAAU,EAAEmC,KAAK;MACjBlC,kBAAkB,EAAE,KAAK;MACzBC,cAAc,EAAE,IAAI,CAAC7D;KACtB;IAED,IAAI,CAACf,qBAAqB,CAAC6E,qBAAqB,CAACN,OAAO,CAAC,CACtDlB,IAAI,CAAC1D,SAAS,CAAC,IAAI,CAACW,QAAQ,CAAC,CAAC,CAC9BoB,SAAS,CAAC;MACTC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,MAAM,KAAK,SAAS,EAAE;UACjC,IAAI,CAACiD,oBAAoB,CAAClD,QAAQ,CAACG,IAAI,CAAC;SACzC,MAAM;UACL,IAAI,CAACgD,kBAAkB,EAAE;;QAE3B,IAAI,CAAC7D,SAAS,GAAG,KAAK;QACtB,IAAI,CAACb,GAAG,CAAC6B,aAAa,EAAE;MAC1B,CAAC;MACDC,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAAC4C,kBAAkB,EAAE;QACzB,IAAI,CAAC7D,SAAS,GAAG,KAAK;QACtB,IAAI,CAACb,GAAG,CAAC6B,aAAa,EAAE;MAC1B;KACD,CAAC;EACN;EAEA;EACA8E,YAAYA,CAACzB,KAAiB;IAC5B,OAAOA,KAAK,CAACxD,IAAI;EACnB;EAEAkF,YAAYA,CAAC1B,KAAiB;IAC5B,OAAO,IAAI,CAACnF,aAAa,CAAC6G,YAAY,CAAC1B,KAAK,CAAC2B,IAAI,CAAC;EACpD;EAEAC,eAAeA,CAAC5B,KAAiB;IAC/B,OAAOA,KAAK,CAAC6B,OAAO,IAAI,IAAI,CAACjH,aAAa,CAACkH,sBAAsB,EAAE;EACrE;EAEAC,gBAAgBA,CAAC/B,KAAiB;IAChC,OAAO,IAAI,CAACnF,aAAa,CAACkH,gBAAgB,CAAC/B,KAAK,CAAC;EACnD;CACD;AApVYzF,uBAAuB,GAAAyH,UAAA,EAxBnC9I,SAAS,CAAC;EACT+I,QAAQ,EAAE,qBAAqB;EAC/BC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACPhJ,YAAY,EACZC,aAAa,EACbC,eAAe,EACfC,aAAa,EACbC,eAAe,EACfC,kBAAkB,EAClBC,cAAc,EACdC,mBAAmB,EACnBC,mBAAmB,EACnBC,gBAAgB,EAChBC,wBAAwB,EACxBuI,iBAAiB,EACjBtI,cAAc,EACdC,wBAAwB,EACxBE,mBAAmB,EACnBC,WAAW,CACZ;EACDmI,WAAW,EAAE,kCAAkC;EAC/CC,SAAS,EAAE,CAAC,kCAAkC;CAC/C,CAAC,C,EACW/H,uBAAuB,CAoVnC;SApVYA,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}