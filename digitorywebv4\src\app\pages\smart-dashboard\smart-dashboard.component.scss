.smart-dashboard-container {
  display: flex;
  flex-direction: column;
  background-color: #f8f9fa;
  height: 100vh; // Take full viewport height
  overflow: hidden; // Prevent outer scrollbar

  // ===== SMART DASHBOARD SPECIFIC MATERIAL OVERRIDES =====
  // Compact form fields with orange theme
  ::ng-deep .mat-mdc-form-field {
    .mat-mdc-text-field-wrapper {
      height: 36px !important;
      min-height: 36px !important;

      .mat-mdc-form-field-infix {
        padding: 6px 12px !important;
        min-height: 24px !important;
        border-top: none !important;
      }

      .mat-mdc-form-field-flex {
        align-items: center !important;
        height: 36px !important;
      }
    }

    // Remove subscript wrapper to save space
    .mat-mdc-form-field-subscript-wrapper {
      display: none !important;
    }

    // Light orange theme for outlines
    .mat-mdc-form-field-outline {
      color: #dee2e6 !important;
    }

    .mat-mdc-form-field-outline-thick {
      color: #ffb366 !important;
    }

    // Orange theme for labels
    .mat-mdc-form-field-label {
      color: #666 !important;
      font-size: 13px !important;
      top: 18px !important;
    }

    &.mat-focused {
      .mat-mdc-form-field-label {
        color: #ffb366 !important;
      }
    }

    // Floating label adjustments
    &.mat-form-field-should-float {
      .mat-mdc-form-field-label {
        transform: translateY(-12px) scale(0.75) !important;
      }
    }
  }

  // Compact select dropdowns
  ::ng-deep .mat-mdc-select {
    .mat-mdc-select-trigger {
      height: 36px !important;
      display: flex !important;
      align-items: center !important;
    }

    .mat-mdc-select-value {
      font-size: 13px !important;
      line-height: 24px !important;
    }

    .mat-mdc-select-arrow {
      color: #ffb366 !important;
    }
  }

  // Select panel styling
  ::ng-deep .mat-mdc-select-panel {
    .mat-mdc-option {
      height: 32px !important;
      line-height: 32px !important;
      font-size: 13px !important;
      padding: 0 16px !important;

      &.mat-mdc-option-active {
        background: rgba(255, 179, 102, 0.1) !important;
        color: #ffb366 !important;
      }

      &:hover {
        background: rgba(255, 179, 102, 0.05) !important;
      }
    }
  }

  // Compact date pickers
  ::ng-deep .mat-mdc-input-element {
    font-size: 13px !important;
    height: 24px !important;
    line-height: 24px !important;
  }

  ::ng-deep .mat-datepicker-toggle {
    .mat-icon {
      color: #ffb366 !important;
      font-size: 18px !important;
      width: 18px !important;
      height: 18px !important;
    }
  }

  // Date picker panel light orange theme
  ::ng-deep .mat-datepicker-content {
    .mat-calendar-header {
      background: #ffb366 !important;
      color: white !important;
    }

    .mat-calendar-body-selected {
      background-color: #ffb366 !important;
      color: white !important;
    }

    .mat-calendar-body-today:not(.mat-calendar-body-selected) {
      border-color: #ffb366 !important;
    }
  }

  // Compact buttons
  ::ng-deep .mat-mdc-raised-button,
  ::ng-deep .mat-mdc-outlined-button {
    height: 32px !important;
    line-height: 32px !important;
    padding: 0 12px !important;
    font-size: 13px !important;

    &.mat-primary {
      background-color: #ffb366 !important;
      color: white !important;

      &:hover {
        background-color: #ffa64d !important;
      }
    }
  }

  ::ng-deep .mat-mdc-outlined-button {
    &.mat-primary {
      border-color: #ffb366 !important;
      color: #ffb366 !important;
      background-color: transparent !important;

      &:hover {
        background-color: rgba(255, 179, 102, 0.05) !important;
      }
    }
  }

  // Main Layout Container
  .main-layout {
    display: flex;
    height: calc(100vh - 40px); // Full height minus actual toolbar height (40px)
    overflow: hidden; // Prevent outer scrollbar

    // Left Sidebar
    .left-sidebar {
      width: 240px;
      background: linear-gradient(135deg, #ffffff 0%, #fff5f0 100%);
      border-right: 1px solid #ffe0cc;
      padding: 16px;
      box-shadow: 2px 0 4px rgba(255, 179, 102, 0.08);
      height: 100%; // Take full available height
      overflow-y: auto; // Allow scrolling within sidebar if needed

      // Custom scrollbar styling to match right panel
      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: #fff2e6;
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb {
        background: #ffb366;
        border-radius: 3px;

        &:hover {
          background: #ffa64d;
        }
      }

      // Dashboard Selection within sidebar
      .dashboard-selection {
        margin-bottom: 20px;
        padding: 0;
        background: none;
        border-radius: 0;
        position: relative;

        .dashboard-dropdown {
          width: 100%;

          ::ng-deep .mat-mdc-form-field-subscript-wrapper {
            display: none;
          }

          ::ng-deep .mat-mdc-text-field-wrapper {
            background: linear-gradient(135deg, #fff8f5 0%, #ffffff 100%);
            border-radius: 6px;
            border: 1px solid #e9ecef;
            transition: all 0.2s ease;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

            &:hover {
              border-color: #ffb366;
              box-shadow: 0 2px 6px rgba(255, 179, 102, 0.1);
            }
          }

          ::ng-deep .mat-mdc-form-field-outline {
            display: none;
          }

          ::ng-deep .mat-mdc-form-field-outline-thick {
            display: none;
          }

          ::ng-deep .mat-mdc-form-field-label {
            color: #666;
            font-weight: 500;
            font-size: 12px;
          }

          ::ng-deep .mat-mdc-select-value {
            color: #333;
            font-weight: 600;
            font-size: 14px;
          }

          ::ng-deep .mat-mdc-select-arrow {
            color: #ffb366;
          }

          ::ng-deep .mat-mdc-form-field-infix {
            padding: 8px 12px;
            min-height: 36px;
          }

          ::ng-deep .mat-mdc-select-trigger {
            height: 36px;
            display: flex;
            align-items: center;
          }

          ::ng-deep .mat-mdc-form-field-flex {
            align-items: center;
          }

          ::ng-deep .mat-focused .mat-mdc-text-field-wrapper {
            border-color: #ffb366 !important;
            box-shadow: 0 3px 8px rgba(255, 179, 102, 0.15) !important;
          }
        }
      }

      // Filters Section
      .filters-section {
        .filters-title {
          display: flex;
          align-items: center;
          gap: 8px;
          margin: 0 0 16px 0;
          font-size: 15px;
          font-weight: 600;
          color: #333;

          mat-icon {
            color: #ffb366;
            font-size: 18px;
            width: 18px;
            height: 18px;
          }

          .filter-count {
            background: #ffb366;
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: bold;
            margin-left: auto;
          }
        }

        .filter-group {
          margin-bottom: 10px;

          .filter-label {
            display: flex;
            align-items: center;
            gap: 6px;
            margin: 0 0 8px 0;
            font-size: 13px;
            font-weight: 500;
            color: #333;

            mat-icon {
              font-size: 14px;
              width: 14px;
              height: 14px;
              color: #ffb366;
            }
          }

          .filter-field {
            width: 100%;
            margin-bottom: 6px;

            ::ng-deep .mat-mdc-form-field-subscript-wrapper {
              display: none;
            }

            ::ng-deep .mat-mdc-text-field-wrapper {
              background-color: white;
              border-radius: 4px;
            }

            ::ng-deep .mat-mdc-form-field-outline {
              color: #dee2e6;
            }

            ::ng-deep .mat-mdc-form-field-outline-thick {
              color: #ffb366;
            }

            ::ng-deep .mat-mdc-form-field-label {
              font-size: 13px;
            }
          }
        }

        .filter-actions {
          margin-top: 20px;
          padding-top: 16px;
          border-top: 1px solid #e9ecef;
          display: flex;
          gap: 8px;

          .search-btn {
            flex: 1;
            background-color: #ffb366 !important;
            color: white !important;
            font-weight: 500;
            padding: 8px;
            border-radius: 4px;
            font-size: 13px;
            border: none;

            &:hover {
              background-color: #ffa64d !important;
            }

            mat-icon {
              font-size: 16px;
              width: 16px;
              height: 16px;
            }
          }

          .reset-filters-btn {
            flex: 1;
            color: #6c757d;
            border-color: #dee2e6;
            font-weight: 500;
            padding: 8px;
            border-radius: 4px;
            font-size: 13px;

            &:hover {
              background-color: #f8f9fa;
              border-color: #adb5bd;
            }

            mat-icon {
              font-size: 16px;
              width: 16px;
              height: 16px;
            }
          }
        }
      }
    }

    // Right Content Area
    .right-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      background-color: #f8f9fa;
      height: 100%; // Take full available height
      overflow: hidden; // Prevent outer scrollbar

      // Top Search Header - Modern Full Width Design
      .search-header {
        background: linear-gradient(135deg, #fff8f5 0%, #ffffff 100%);
        border-bottom: 1px solid #e9ecef;
        padding: 10px 20px;
        display: flex;
        align-items: center;
        gap: 16px;
        min-height: 50px;
        box-shadow: 0 1px 3px rgba(255, 179, 102, 0.05);
        overflow: hidden; // Prevent overflow

        .assistant-info {
          display: flex;
          align-items: center;
          gap: 8px;
          flex-shrink: 0;

          .assistant-icon {
            color: #ffb366;
            font-size: 18px;
            width: 18px;
            height: 18px;
          }

          .assistant-text {
            display: flex;
            align-items: center;
            gap: 10px;

            .assistant-title {
              font-size: 14px;
              font-weight: 600;
              color: #333;
              white-space: nowrap;
            }

            .assistant-status {
              font-size: 10px;
              color: #28a745;
              font-weight: 500;
              background: #e8f5e8;
              padding: 2px 8px;
              border-radius: 12px;
              display: inline-block;
              white-space: nowrap;
            }
          }
        }

        .search-container {
          flex: 1;
          min-width: 0; // Prevent flex item from overflowing
          max-width: calc(100% - 200px); // Reserve space for assistant info

          .search-field {
            width: 100%;
            max-width: 100%;

            ::ng-deep .mat-mdc-form-field-subscript-wrapper {
              display: none;
            }

            ::ng-deep .mat-mdc-text-field-wrapper {
              background-color: white;
              border-radius: 24px;
              height: 36px;
              border: 1px solid #e9ecef;
              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
              transition: all 0.3s ease;
              width: 100%;
              max-width: 100%;

              &:hover {
                border-color: #ffb366;
                box-shadow: 0 4px 12px rgba(255, 179, 102, 0.1);
              }
            }

            ::ng-deep .mat-mdc-form-field-outline {
              display: none;
            }

            ::ng-deep .mat-mdc-form-field-outline-thick {
              display: none;
            }

            ::ng-deep .mat-mdc-form-field-infix {
              padding: 6px 16px;
              border-top: none;
              width: 100%;
              max-width: 100%;
            }

            ::ng-deep .mat-mdc-form-field-flex {
              align-items: center;
              height: 36px;
              width: 100%;
            }

            ::ng-deep input {
              font-size: 14px;
              color: #333;
              font-weight: 400;
              width: 100%;
            }

            ::ng-deep input::placeholder {
              color: #999;
              font-size: 14px;
              font-weight: 400;
            }

            ::ng-deep .mat-focused .mat-mdc-text-field-wrapper {
              border-color: #ffb366 !important;
              box-shadow: 0 4px 16px rgba(255, 179, 102, 0.15) !important;
            }

            .search-icon {
              color: #999;
              cursor: pointer;
              font-size: 16px;
              transition: color 0.2s ease;

              &:hover {
                color: #ffb366;
              }
            }
          }
        }
      }

      // Dashboard Content Area
      .dashboard-content-area {
        padding: 20px;
        flex: 1; // Take remaining height after search header
        overflow-y: auto; // Only this area should scroll

        // Custom scrollbar styling
        &::-webkit-scrollbar {
          width: 6px;
        }

        &::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 3px;
        }

        &::-webkit-scrollbar-thumb {
          background: #ffb366;
          border-radius: 3px;

          &:hover {
            background: #ffa64d;
          }
        }

        .loading-container {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 400px;
          gap: 20px;
          background: linear-gradient(135deg, #fff8f5 0%, #ffffff 100%);
          border-radius: 12px;
          border: 1px solid #ffe0cc;
          margin: 20px;
          box-shadow: 0 4px 12px rgba(255, 179, 102, 0.08);

          ::ng-deep .mat-mdc-progress-spinner {
            .mdc-circular-progress__determinate-circle,
            .mdc-circular-progress__indeterminate-circle-graphic {
              stroke: #ffb366 !important;
            }
          }

          p {
            color: #333;
            font-size: 16px;
            font-weight: 500;
            margin: 0;
            text-align: center;
            background: linear-gradient(135deg, #ffb366 0%, #ffc999 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
          }
        }

        .dashboard-grid {
          .summary-cards-row {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 16px;
            margin-bottom: 20px;

            .summary-card {
              background: white;
              border-radius: 8px;
              border: 1px solid #e9ecef;
              transition: box-shadow 0.2s ease;
              position: relative;

              &:hover {
                box-shadow: 0 4px 12px rgba(0,0,0,0.1);
              }

              ::ng-deep .mat-mdc-card-content {
                padding: 16px !important;
              }

              .card-content {
                display: flex;
                align-items: center;
                gap: 12px;

                .card-icon {
                  width: 40px;
                  height: 40px;
                  border-radius: 8px;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  background: linear-gradient(135deg, #ffb366 0%, #ffc999 100%);

                  mat-icon {
                    font-size: 20px;
                    width: 20px;
                    height: 20px;
                    color: white;
                  }
                }

                .card-info {
                  flex: 1;

                  .card-value {
                    font-size: 20px;
                    font-weight: 600;
                    color: #333;
                    margin-bottom: 4px;
                    line-height: 1.2;
                  }

                  .card-label {
                    font-size: 12px;
                    color: #6c757d;
                    font-weight: 500;
                    line-height: 1.3;
                  }
                }
              }
            }
          }

          .charts-grid {
            display: grid;
            grid-template-columns: repeat(12, 1fr);
            gap: 16px;

            .chart-card {
              background: white;
              border-radius: 12px;
              border: 1px solid #e9ecef;
              transition: box-shadow 0.2s ease;
              height: 400px;

              &:hover {
                box-shadow: 0 4px 12px rgba(255, 179, 102, 0.1);
              }

              &.full-width {
                grid-column: span 12;
              }

              &.half-width {
                grid-column: span 6;
              }

              &.third-width {
                grid-column: span 4;
              }

              .chart-title {
                font-size: 14px;
                font-weight: 600;
                color: #333;
                margin: 0;
              }

              .chart-container {
                position: relative;
                height: 320px;
                padding: 16px;

                canvas {
                  width: 100% !important;
                  height: 100% !important;
                }

                .no-data-message {
                  display: flex;
                  flex-direction: column;
                  align-items: center;
                  justify-content: center;
                  height: 280px;
                  color: #adb5bd;
                  text-align: center;

                  mat-icon {
                    font-size: 40px;
                    width: 40px;
                    height: 40px;
                    margin-bottom: 8px;
                    color: #ffb366;
                  }

                  p {
                    margin: 0;
                    font-size: 13px;
                    font-weight: 500;
                  }
                }
              }
            }
          }


        }

        .empty-state {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 400px;
          text-align: center;
          color: #666;

          .empty-icon {
            font-size: 64px;
            width: 64px;
            height: 64px;
            margin-bottom: 16px;
            opacity: 0.5;
          }

          h3 {
            margin: 0 0 8px 0;
            font-size: 20px;
          }

          p {
            margin: 0 0 24px 0;
            font-size: 14px;
          }
        }
      }
    }
  }
}

// Responsive Design
@media (max-width: 1400px) {
  .smart-dashboard-container .main-layout .right-content .dashboard-content-area .dashboard-grid {
    .summary-cards-row {
      grid-template-columns: repeat(2, 1fr);
    }

    .charts-grid .chart-card.half-width,
    .charts-grid .chart-card.third-width {
      grid-column: span 12;
    }
  }
}

@media (max-width: 1024px) {
  .smart-dashboard-container {
    .main-layout .right-content .search-header {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;

      .search-container .search-field {
        width: 100%;
      }
    }

    .main-layout {
      .left-sidebar {
        width: 220px;
      }
    }
  }
}

@media (max-width: 768px) {
  .smart-dashboard-container {
    .main-layout {
      flex-direction: column;

      .left-sidebar {
        width: 100%;
        border-right: none;
        border-bottom: 1px solid #e9ecef;
        padding: 12px;
      }

      .right-content {
        .search-header {
          flex-direction: column;
          gap: 12px;
          padding: 12px 16px;

          .search-container .search-field {
            width: 100%;
          }
        }

        .dashboard-content-area {
          padding: 12px;

          .dashboard-grid {
            .summary-cards-row {
              grid-template-columns: 1fr;
              gap: 12px;
            }

            .charts-grid {
              gap: 12px;

              .chart-card {
                grid-column: span 12 !important;
              }
            }
          }
        }
      }
    }
  }
}

// Clean Chart Styling
::ng-deep {
  .chart-container canvas {
    max-width: 100% !important;
    height: auto !important;
  }
}


